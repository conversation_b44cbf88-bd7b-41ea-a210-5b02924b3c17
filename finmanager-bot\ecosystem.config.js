module.exports = {
  apps: [{
    name: 'finmanager-bot',
    script: 'enterprise-bot.js',
    instances: 1,
    exec_mode: 'fork',
    
    // Memory optimization for E2 Micro (1GB RAM)
    max_memory_restart: '800M',
    node_args: '--max-old-space-size=512',
    
    // Environment variables
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      HEALTH_PORT: 3001
    },
    
    // Logging configuration
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    merge_logs: true,
    
    // Auto-restart configuration
    watch: false,
    ignore_watch: ['node_modules', 'logs', '.git'],
    
    // Restart policy
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Health monitoring
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true,
    
    // Process management
    kill_timeout: 5000,
    listen_timeout: 3000,
    
    // Advanced options for Oracle Cloud E2 Micro
    max_old_space_size: 512,
    
    // Cron restart (optional - restart daily at 3 AM)
    cron_restart: '0 3 * * *',
    
    // Source map support
    source_map_support: false,
    
    // Disable automatic restart on file changes
    autorestart: true,
    
    // Time before sending SIGKILL
    kill_timeout: 5000
  }]
};
