#!/bin/bash

# =============================================================================
# FiNManageR Telegram Bot - Quick Setup Script
# =============================================================================
# This script quickly configures your bot with the known credentials
# and starts it up for immediate testing
#
# Usage: ./quick-setup.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Configuration
BOT_DIR="$HOME/finmanager-bot"
KNOWN_SUPABASE_URL="https://rprzvyfjdvjoxctawrvo.supabase.co"
PRODUCTION_BOT_TOKEN="8178268192:AAGbbBrZDY400gWlHSIBMgToFDav1XnalKE"
PRODUCTION_BOT_USERNAME="Myfnmbot"

echo "🚀 =============================================="
echo "🚀  FiNManageR Bot Quick Setup"
echo "🚀 =============================================="
echo ""

# Check if bot directory exists
if [[ ! -d "$BOT_DIR" ]]; then
    error "Bot directory not found: $BOT_DIR"
    echo "Please run the initialization script first: ./oracle-cloud-init.sh"
fi

cd "$BOT_DIR"

# =============================================================================
# ENVIRONMENT CONFIGURATION
# =============================================================================

log "⚙️ Configuring environment with known credentials..."

# Create optimized .env file
cat > .env << EOF
# FiNManageR Telegram Bot Environment Configuration
# Generated by quick-setup.sh on $(date)

# Environment
NODE_ENV=production
PORT=3000
HEALTH_PORT=3001

# Memory optimization for E2 Micro
NODE_OPTIONS=--max-old-space-size=512

# Telegram Bot Configuration (PRODUCTION)
TELEGRAM_BOT_TOKEN=$PRODUCTION_BOT_TOKEN
BOT_USERNAME=$PRODUCTION_BOT_USERNAME

# Supabase Configuration
SUPABASE_URL=$KNOWN_SUPABASE_URL
SUPABASE_SERVICE_ROLE_KEY=PLEASE_ADD_YOUR_SERVICE_ROLE_KEY_HERE

# Logging (optimized for E2 Micro)
ENABLE_LOGGING=true
LOG_LEVEL=info

# Features
ENABLE_OCR=true
ENABLE_VOICE=true
ENABLE_AI_INSIGHTS=true
ENABLE_PUSH_NOTIFICATIONS=true

# Oracle Cloud specific
ORACLE_CLOUD=true
INSTANCE_TYPE=E2_MICRO

# Security
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=30
SESSION_EXPIRE_DAYS=30

# Monitoring
ENABLE_MONITORING=true
EOF

log "✅ Environment file configured with known values"

# =============================================================================
# DEPENDENCY CHECK AND INSTALL
# =============================================================================

log "📦 Checking and installing dependencies..."

# Check if node_modules exists
if [[ ! -d "node_modules" ]]; then
    log "Installing dependencies..."
    NODE_OPTIONS="--max-old-space-size=512" npm install --production
else
    log "✅ Dependencies already installed"
fi

# =============================================================================
# MANAGEMENT SCRIPTS CHECK
# =============================================================================

log "🛠️ Ensuring management scripts are executable..."

SCRIPTS=("manage-bot.sh" "monitor-system.sh")
for script in "${SCRIPTS[@]}"; do
    if [[ -f "$script" ]]; then
        chmod +x "$script"
        log "✅ $script is executable"
    else
        warn "$script not found - may need to run full initialization"
    fi
done

# =============================================================================
# SYSTEM CHECKS
# =============================================================================

log "🔍 Performing quick system checks..."

# Check memory
TOTAL_MEM=$(free -m | awk '/^Mem:/ {print $2}')
log "Available memory: ${TOTAL_MEM}MB"

# Check swap
if [[ $(swapon --show | wc -l) -gt 0 ]]; then
    SWAP_SIZE=$(free -h | awk '/^Swap:/ {print $2}')
    log "Swap configured: $SWAP_SIZE"
else
    warn "No swap configured - may need to run full initialization"
fi

# Check Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    log "Node.js version: $NODE_VERSION"
else
    error "Node.js not installed - run full initialization script"
fi

# Check PM2
if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    log "PM2 version: $PM2_VERSION"
else
    error "PM2 not installed - run full initialization script"
fi

# =============================================================================
# BOT STARTUP
# =============================================================================

log "🚀 Starting the bot..."

# Stop any existing bot process
if pm2 list | grep -q "finmanager-bot"; then
    log "Stopping existing bot process..."
    pm2 stop finmanager-bot 2>/dev/null || true
    pm2 delete finmanager-bot 2>/dev/null || true
fi

# Start the bot
if [[ -f "manage-bot.sh" ]]; then
    log "Starting bot using management script..."
    ./manage-bot.sh start
else
    log "Starting bot directly with PM2..."
    pm2 start enterprise-bot.js --name "finmanager-bot" --max-memory-restart 800M
fi

# Wait a moment for startup
sleep 3

# =============================================================================
# VERIFICATION
# =============================================================================

log "🔍 Verifying bot startup..."

# Check PM2 status
if pm2 list | grep "finmanager-bot" | grep -q "online"; then
    log "✅ Bot is running in PM2"
else
    error "Bot failed to start - check logs with: pm2 logs finmanager-bot"
fi

# Check health endpoint
sleep 2
if curl -s http://localhost:3001/health &> /dev/null; then
    log "✅ Health check endpoint responding"
    
    # Get health status
    HEALTH_STATUS=$(curl -s http://localhost:3001/health | grep -o '"status":"[^"]*"' | cut -d'"' -f4)
    if [[ "$HEALTH_STATUS" == "healthy" ]]; then
        log "✅ Bot reports healthy status"
    else
        warn "Bot health status: $HEALTH_STATUS"
    fi
else
    warn "Health check endpoint not responding yet (may need more time)"
fi

# =============================================================================
# FINAL INSTRUCTIONS
# =============================================================================

echo ""
echo "🎉 =============================================="
echo "🎉  Quick Setup Complete!"
echo "🎉 =============================================="
echo ""

# Check if service role key needs to be added
if grep -q "PLEASE_ADD_YOUR_SERVICE_ROLE_KEY_HERE" .env; then
    echo "⚠️  IMPORTANT: You still need to add your Supabase Service Role Key!"
    echo ""
    echo "1️⃣  Edit the environment file:"
    echo "   nano .env"
    echo ""
    echo "2️⃣  Replace this line:"
    echo "   SUPABASE_SERVICE_ROLE_KEY=PLEASE_ADD_YOUR_SERVICE_ROLE_KEY_HERE"
    echo ""
    echo "3️⃣  With your actual service role key:"
    echo "   SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key"
    echo ""
    echo "4️⃣  Restart the bot:"
    echo "   ./manage-bot.sh restart"
    echo ""
else
    echo "✅ Configuration appears complete!"
    echo ""
fi

echo "📊 Bot Status:"
pm2 list | grep finmanager-bot || echo "Bot not found in PM2"
echo ""

echo "🔗 Health Check:"
echo "   Local:  http://localhost:3001/health"
echo "   Public: http://$(curl -s ifconfig.me 2>/dev/null || echo "YOUR_PUBLIC_IP"):3001/health"
echo ""

echo "🤖 Telegram Bot:"
echo "   Username: @$PRODUCTION_BOT_USERNAME"
echo "   Test: Send /start to your bot in Telegram"
echo ""

echo "📋 Management Commands:"
echo "   ./manage-bot.sh status    # Check bot status"
echo "   ./manage-bot.sh logs      # View bot logs"
echo "   ./manage-bot.sh restart   # Restart bot"
echo "   ./manage-bot.sh health    # Check health"
echo "   ./monitor-system.sh       # System monitor"
echo ""

echo "🔧 Next Steps:"
if grep -q "PLEASE_ADD_YOUR_SERVICE_ROLE_KEY_HERE" .env; then
    echo "   1. Add your Supabase Service Role Key to .env"
    echo "   2. Restart the bot: ./manage-bot.sh restart"
    echo "   3. Test the bot in Telegram"
else
    echo "   1. Test the bot in Telegram: @$PRODUCTION_BOT_USERNAME"
    echo "   2. Monitor with: ./manage-bot.sh logs"
    echo "   3. Check system resources: ./monitor-system.sh"
fi

echo ""
echo "✅ Your FiNManageR bot should now be running!"
echo "=============================================="
