#!/bin/bash

# =============================================================================
# FiNManageR Telegram Bot - Oracle Cloud E2 Micro Initialization Script
# =============================================================================
# This script automates the complete setup of your Telegram bot on Oracle Cloud
# E2 Micro instance (1GB RAM, 1/8 OCPU)
#
# Usage: 
#   1. Upload this script to your Oracle Cloud instance
#   2. Make it executable: chmod +x oracle-cloud-init.sh
#   3. Run it: ./oracle-cloud-init.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

# Configuration variables
BOT_DIR="$HOME/finmanager-bot"
REPO_URL="https://github.com/bbsivajibb7/finmanager-bot.git"
NODE_VERSION="18"
SWAP_SIZE="1G"

# =============================================================================
# PHASE 1: System Information and Prerequisites
# =============================================================================

log "🚀 Starting FiNManageR Telegram Bot initialization on Oracle Cloud E2 Micro"
log "📊 Checking system specifications..."

# Display system info
echo "=== System Information ==="
echo "OS: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo "CPU Cores: $(nproc)"
echo "Total Memory: $(free -h | awk '/^Mem:/ {print $2}')"
echo "Available Memory: $(free -h | awk '/^Mem:/ {print $7}')"
echo "Disk Space: $(df -h / | awk 'NR==2 {print $4 " available of " $2}')"
echo "=========================="

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   error "This script should not be run as root. Please run as ubuntu user."
fi

# Check internet connectivity
log "🌐 Checking internet connectivity..."
if ! ping -c 1 google.com &> /dev/null; then
    error "No internet connection. Please check your network settings."
fi

# Check if this is a re-run
if [[ -f "$BOT_DIR/.setup_completed" ]]; then
    warn "Setup appears to have been run before. Continuing with updates..."
    RERUN=true
else
    RERUN=false
fi

log "✅ Prerequisites check completed"

# =============================================================================
# PHASE 2: System Updates and Basic Setup
# =============================================================================

log "📦 Updating system packages..."
sudo apt update && sudo apt upgrade -y

log "🔧 Installing essential packages..."
sudo apt install -y \
    curl \
    wget \
    git \
    htop \
    nano \
    unzip \
    software-properties-common \
    apt-transport-https \
    ca-certificates \
    gnupg \
    lsb-release \
    build-essential

# =============================================================================
# PHASE 3: Memory Optimization (Critical for E2 Micro)
# =============================================================================

log "💾 Setting up swap file for memory optimization..."

# Check if swap already exists
if [[ $(swapon --show | wc -l) -eq 0 ]]; then
    log "Creating ${SWAP_SIZE} swap file..."
    sudo fallocate -l $SWAP_SIZE /swapfile
    sudo chmod 600 /swapfile
    sudo mkswap /swapfile
    sudo swapon /swapfile

    # Make swap permanent
    echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab

    # Optimize swap usage
    echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
    echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf

    log "✅ Swap file created and configured"
else
    log "✅ Swap already configured"
    # Verify swap size
    CURRENT_SWAP=$(free -h | awk '/^Swap:/ {print $2}')
    log "Current swap size: $CURRENT_SWAP"
fi

# Display memory info
log "📊 Memory configuration:"
free -h

# =============================================================================
# PHASE 4: Node.js Installation
# =============================================================================

log "📦 Installing Node.js ${NODE_VERSION}..."

# Check if Node.js is already installed with correct version
if command -v node &> /dev/null; then
    CURRENT_NODE_VERSION=$(node --version | sed 's/v//' | cut -d. -f1)
    if [[ $CURRENT_NODE_VERSION -eq $NODE_VERSION ]]; then
        log "✅ Node.js ${NODE_VERSION} already installed: $(node --version)"
        log "✅ NPM already installed: $(npm --version)"
    else
        warn "Node.js version mismatch. Current: v$CURRENT_NODE_VERSION, Expected: v$NODE_VERSION"
        log "Updating Node.js..."
        # Remove existing version
        sudo apt remove -y nodejs npm 2>/dev/null || true
        # Install correct version
        curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
        sudo apt-get install -y nodejs
        log "✅ Node.js updated: $(node --version)"
        log "✅ NPM updated: $(npm --version)"
    fi
else
    log "Installing Node.js ${NODE_VERSION}..."
    # Install Node.js from NodeSource
    curl -fsSL https://deb.nodesource.com/setup_${NODE_VERSION}.x | sudo -E bash -
    sudo apt-get install -y nodejs

    # Verify installation
    NODE_VER=$(node --version)
    NPM_VER=$(npm --version)

    log "✅ Node.js installed: $NODE_VER"
    log "✅ NPM installed: $NPM_VER"
fi

# =============================================================================
# PHASE 5: PM2 Installation and Configuration
# =============================================================================

log "🔄 Installing PM2 process manager..."

# Check if PM2 is already installed
if command -v pm2 &> /dev/null; then
    log "✅ PM2 already installed: $(pm2 --version)"

    # Check if logrotate is configured
    if pm2 list | grep -q "pm2-logrotate" 2>/dev/null; then
        log "✅ PM2 logrotate already configured"
    else
        log "Configuring PM2 logrotate..."
        pm2 install pm2-logrotate
        pm2 set pm2-logrotate:max_size 10M
        pm2 set pm2-logrotate:retain 7
        log "✅ PM2 logrotate configured"
    fi
else
    log "Installing PM2..."
    sudo npm install -g pm2

    # Configure PM2 for memory optimization
    pm2 install pm2-logrotate
    pm2 set pm2-logrotate:max_size 10M
    pm2 set pm2-logrotate:retain 7

    log "✅ PM2 installed and configured"
fi

# =============================================================================
# PHASE 6: Firewall Configuration
# =============================================================================

log "🔒 Configuring firewall..."

# Enable UFW
sudo ufw --force enable

# Allow SSH
sudo ufw allow ssh

# Allow health check port
sudo ufw allow 3001/tcp

# Allow HTTP (optional, for future web interface)
sudo ufw allow 80/tcp

# Show firewall status
sudo ufw status

log "✅ Firewall configured"

# =============================================================================
# PHASE 7: Bot Repository Setup
# =============================================================================

log "📥 Setting up bot repository..."

# Remove existing directory if it exists
if [[ -d "$BOT_DIR" ]]; then
    warn "Existing bot directory found. Backing up..."
    mv "$BOT_DIR" "${BOT_DIR}.backup.$(date +%s)"
fi

# Clone repository
log "📥 Cloning bot repository..."
git clone "$REPO_URL" "$BOT_DIR"

# Navigate to bot directory
cd "$BOT_DIR"

log "✅ Repository cloned successfully"

# =============================================================================
# PHASE 8: Dependencies Installation
# =============================================================================

log "📦 Installing bot dependencies..."

# Set npm to use less memory
npm config set fund false
npm config set audit false

# Install dependencies with memory optimization
NODE_OPTIONS="--max-old-space-size=512" npm install --production

log "✅ Dependencies installed"

# =============================================================================
# PHASE 9: Environment Configuration
# =============================================================================

log "⚙️ Setting up environment configuration..."

# Create .env file from template
if [[ -f ".env.example" ]]; then
    cp .env.example .env
    log "✅ Environment file created from template"
else
    # Create basic .env file
    cat > .env << EOF
# FiNManageR Telegram Bot Environment Configuration

# Environment
NODE_ENV=production
PORT=3000
HEALTH_PORT=3001

# Memory optimization for E2 Micro
NODE_OPTIONS=--max-old-space-size=512

# Telegram Bot Configuration
# IMPORTANT: Replace with your actual bot token
TELEGRAM_BOT_TOKEN=YOUR_BOT_TOKEN_HERE
BOT_USERNAME=YOUR_BOT_USERNAME_HERE

# Supabase Configuration
# IMPORTANT: Replace with your actual Supabase credentials
SUPABASE_URL=YOUR_SUPABASE_URL_HERE
SUPABASE_SERVICE_ROLE_KEY=YOUR_SUPABASE_SERVICE_KEY_HERE

# Logging (optimized for E2 Micro)
ENABLE_LOGGING=true
LOG_LEVEL=warn

# Features
ENABLE_OCR=true
ENABLE_VOICE=true
ENABLE_AI_INSIGHTS=true

# Oracle Cloud specific
ORACLE_CLOUD=true
INSTANCE_TYPE=E2_MICRO
EOF
    log "✅ Basic environment file created"
fi

warn "🔧 IMPORTANT: You need to edit .env file with your actual credentials!"
warn "📝 Run: nano $BOT_DIR/.env"

# =============================================================================
# PHASE 10: PM2 Ecosystem Configuration
# =============================================================================

log "🔄 Creating PM2 ecosystem configuration..."

cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'finmanager-bot',
    script: 'enterprise-bot.js',
    instances: 1,
    exec_mode: 'fork',
    
    // Memory optimization for E2 Micro
    max_memory_restart: '800M',
    node_args: '--max-old-space-size=512',
    
    // Environment
    env: {
      NODE_ENV: 'production',
      PORT: 3000,
      HEALTH_PORT: 3001
    },
    
    // Logging
    log_file: './logs/combined.log',
    out_file: './logs/out.log',
    error_file: './logs/error.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    
    // Auto-restart configuration
    watch: false,
    ignore_watch: ['node_modules', 'logs'],
    
    // Restart policy
    restart_delay: 4000,
    max_restarts: 10,
    min_uptime: '10s',
    
    // Health monitoring
    health_check_grace_period: 3000,
    health_check_fatal_exceptions: true
  }]
};
EOF

# Create logs directory
mkdir -p logs

log "✅ PM2 ecosystem configuration created"

# =============================================================================
# PHASE 11: Utility Scripts Creation
# =============================================================================

log "🛠️ Creating utility scripts..."

# Bot management script
cat > manage-bot.sh << 'EOF'
#!/bin/bash

# FiNManageR Bot Management Script

case "$1" in
    start)
        echo "🚀 Starting FiNManageR Bot..."
        pm2 start ecosystem.config.js
        ;;
    stop)
        echo "🛑 Stopping FiNManageR Bot..."
        pm2 stop finmanager-bot
        ;;
    restart)
        echo "🔄 Restarting FiNManageR Bot..."
        pm2 restart finmanager-bot
        ;;
    status)
        echo "📊 Bot Status:"
        pm2 status
        ;;
    logs)
        echo "📋 Bot Logs:"
        pm2 logs finmanager-bot --lines 50
        ;;
    monitor)
        echo "📊 Opening PM2 Monitor..."
        pm2 monit
        ;;
    update)
        echo "📥 Updating bot from repository..."
        git pull origin main
        npm install --production
        pm2 restart finmanager-bot
        echo "✅ Bot updated successfully!"
        ;;
    health)
        echo "🏥 Checking bot health..."
        curl -s http://localhost:3001/health | jq . || echo "Health check failed"
        ;;
    system)
        echo "💻 System Resources:"
        echo "Memory Usage:"
        free -h
        echo ""
        echo "Disk Usage:"
        df -h /
        echo ""
        echo "CPU Usage:"
        top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5 $6 $7 $8}'
        ;;
    *)
        echo "Usage: $0 {start|stop|restart|status|logs|monitor|update|health|system}"
        echo ""
        echo "Commands:"
        echo "  start   - Start the bot"
        echo "  stop    - Stop the bot"
        echo "  restart - Restart the bot"
        echo "  status  - Show bot status"
        echo "  logs    - Show recent logs"
        echo "  monitor - Open PM2 monitor"
        echo "  update  - Update bot from git"
        echo "  health  - Check bot health"
        echo "  system  - Show system resources"
        exit 1
        ;;
esac
EOF

chmod +x manage-bot.sh

# System monitoring script
cat > monitor-system.sh << 'EOF'
#!/bin/bash

# System Monitoring Script for E2 Micro

echo "=== Oracle Cloud E2 Micro System Monitor ==="
echo "Timestamp: $(date)"
echo ""

echo "💾 Memory Usage:"
free -h
echo ""

echo "💽 Disk Usage:"
df -h /
echo ""

echo "🔄 CPU Usage:"
top -bn1 | head -n 5
echo ""

echo "🔥 Top Processes by Memory:"
ps aux --sort=-%mem | head -n 10
echo ""

echo "🌐 Network Connections:"
ss -tuln | grep :3001
echo ""

echo "📊 PM2 Status:"
pm2 status
echo ""

echo "🔍 Bot Health Check:"
curl -s http://localhost:3001/health 2>/dev/null | jq . || echo "Health check failed"
echo ""

echo "📈 System Load:"
uptime
echo ""

echo "🔄 Swap Usage:"
swapon --show
echo ""
EOF

chmod +x monitor-system.sh

log "✅ Utility scripts created"

# =============================================================================
# PHASE 12: Final Setup and Instructions
# =============================================================================

log "🎯 Completing final setup..."

# Set up PM2 startup
pm2 startup | tail -n 1 | bash || warn "PM2 startup setup may need manual configuration"

# Create desktop shortcut for easy access
cat > ~/Desktop/bot-manager.desktop << EOF
[Desktop Entry]
Version=1.0
Type=Application
Name=FiNManageR Bot Manager
Comment=Manage FiNManageR Telegram Bot
Exec=gnome-terminal -- bash -c "cd $BOT_DIR && ./manage-bot.sh status; bash"
Icon=utilities-terminal
Terminal=false
Categories=Utility;
EOF

chmod +x ~/Desktop/bot-manager.desktop 2>/dev/null || true

log "✅ Installation completed successfully!"

# Create completion marker
touch "$BOT_DIR/.setup_completed"
echo "$(date)" > "$BOT_DIR/.setup_completed"

# =============================================================================
# FINAL INSTRUCTIONS
# =============================================================================

echo ""
echo "🎉 =============================================="
echo "🎉  FiNManageR Bot Installation Complete!"
echo "🎉 =============================================="
echo ""
echo "📋 NEXT STEPS:"
echo ""
echo "1️⃣  Configure your bot credentials:"
echo "   cd $BOT_DIR"
echo "   nano .env"
echo ""
echo "2️⃣  Add your actual values:"
echo "   - TELEGRAM_BOT_TOKEN=your_actual_token"
echo "   - BOT_USERNAME=your_bot_username"
echo "   - SUPABASE_URL=your_supabase_url"
echo "   - SUPABASE_SERVICE_ROLE_KEY=your_service_key"
echo ""
echo "3️⃣  Start your bot:"
echo "   ./manage-bot.sh start"
echo ""
echo "📊 USEFUL COMMANDS:"
echo "   ./manage-bot.sh status    - Check bot status"
echo "   ./manage-bot.sh logs      - View bot logs"
echo "   ./manage-bot.sh monitor   - Open PM2 monitor"
echo "   ./manage-bot.sh health    - Check bot health"
echo "   ./manage-bot.sh system    - View system resources"
echo "   ./monitor-system.sh       - Detailed system monitor"
echo ""
echo "🔗 HEALTH CHECK URL:"
echo "   http://$(curl -s ifconfig.me):3001/health"
echo ""
echo "💡 TIPS FOR E2 MICRO:"
echo "   - Monitor memory usage regularly"
echo "   - Bot auto-restarts if memory exceeds 800MB"
echo "   - Swap file provides extra memory buffer"
echo "   - Use 'manage-bot.sh system' to check resources"
echo ""
echo "🆘 SUPPORT:"
echo "   - Check logs: ./manage-bot.sh logs"
echo "   - Monitor resources: ./monitor-system.sh"
echo "   - Restart if needed: ./manage-bot.sh restart"
echo ""
echo "✅ Your Oracle Cloud E2 Micro instance is ready!"
echo "=============================================="
