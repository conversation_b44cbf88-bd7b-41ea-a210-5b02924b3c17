#!/bin/bash

# =============================================================================
# FiNManageR Bot - Account Linking Debug Script
# =============================================================================
# Debug account linking issues and test database connectivity
#
# Usage: ./debug-account-linking.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "🔍 =============================================="
echo "🔍  Account Linking Debug & Diagnosis"
echo "🔍 =============================================="
echo ""

# =============================================================================
# PHASE 1: Check Bot Logs for Link Commands
# =============================================================================

header "Checking Bot Logs for Link Commands"

info "Searching for recent /link commands in bot logs..."

# Check for link commands in logs
LINK_ATTEMPTS=$(pm2 logs "$BOT_NAME" --lines 200 2>/dev/null | grep -i "/link" | wc -l)
echo "Total /link attempts found in logs: $LINK_ATTEMPTS"

if [[ $LINK_ATTEMPTS -gt 0 ]]; then
    echo ""
    echo "Recent /link attempts:"
    pm2 logs "$BOT_NAME" --lines 200 2>/dev/null | grep -i "/link" | tail -10
    echo ""
else
    warn "No /link commands found in recent logs"
fi

# Check for errors related to linking
echo "Checking for linking-related errors:"
LINK_ERRORS=$(pm2 logs "$BOT_NAME" --lines 200 2>/dev/null | grep -i "link.*error\|error.*link" | wc -l)
if [[ $LINK_ERRORS -gt 0 ]]; then
    echo "Link-related errors found:"
    pm2 logs "$BOT_NAME" --lines 200 2>/dev/null | grep -i "link.*error\|error.*link" | tail -5
else
    log "No link-related errors found"
fi

echo ""

# =============================================================================
# PHASE 2: Check Database Connectivity
# =============================================================================

header "Testing Database Connectivity"

# Test health endpoint for database status
info "Testing database connection via health endpoint..."

HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health" 2>/dev/null)

if [[ -n "$HEALTH_RESPONSE" ]]; then
    echo "Health response received:"
    echo "$HEALTH_RESPONSE" | jq . 2>/dev/null || echo "$HEALTH_RESPONSE"
    echo ""
    
    if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
        log "Database connection confirmed via health check"
    else
        error "Database connection not confirmed in health check"
    fi
else
    error "No health response received"
fi

echo ""

# =============================================================================
# PHASE 3: Check Environment Configuration
# =============================================================================

header "Checking Environment Configuration"

info "Verifying critical environment variables..."

if [[ -f ".env" ]]; then
    # Check Supabase configuration
    if grep -q "SUPABASE_URL=https://rprzvyfjdvjoxctawrvo.supabase.co" .env; then
        log "Supabase URL configured correctly"
    else
        error "Supabase URL not configured correctly"
        echo "Current SUPABASE_URL:"
        grep "SUPABASE_URL" .env || echo "Not found"
    fi
    
    if grep -q "SUPABASE_SERVICE_ROLE_KEY=" .env && ! grep -q "YOUR_" .env; then
        log "Supabase service role key appears to be configured"
        # Show first few characters for verification
        SERVICE_KEY=$(grep "SUPABASE_SERVICE_ROLE_KEY=" .env | cut -d'=' -f2)
        echo "Service key starts with: ${SERVICE_KEY:0:20}..."
    else
        error "Supabase service role key not configured properly"
    fi
    
    # Check bot token
    if grep -q "TELEGRAM_BOT_TOKEN=8178268192" .env; then
        log "Production bot token configured"
    else
        error "Production bot token not configured"
        echo "Current bot token starts with:"
        grep "TELEGRAM_BOT_TOKEN" .env | cut -d'=' -f2 | cut -c1-15 || echo "Not found"
    fi
else
    error ".env file not found"
fi

echo ""

# =============================================================================
# PHASE 4: Check Bot Implementation
# =============================================================================

header "Checking Bot Implementation"

info "Checking which bot script is running..."

# Check which bot file is being used
if pm2 show "$BOT_NAME" 2>/dev/null | grep -q "script"; then
    SCRIPT_PATH=$(pm2 show "$BOT_NAME" 2>/dev/null | grep "script" | awk '{print $3}')
    echo "Bot is running script: $SCRIPT_PATH"
    
    if [[ -f "$SCRIPT_PATH" ]]; then
        log "Bot script file exists"
        
        # Check if the script has link command implementation
        if grep -q "/link" "$SCRIPT_PATH"; then
            log "Link command found in bot script"
            
            # Show the link command implementation
            echo ""
            echo "Link command implementation:"
            grep -A 20 -B 5 "/link" "$SCRIPT_PATH" | head -30
            echo ""
        else
            error "No link command implementation found in bot script"
        fi
    else
        error "Bot script file not found: $SCRIPT_PATH"
    fi
else
    warn "Could not determine bot script path"
fi

echo ""

# =============================================================================
# PHASE 5: Test Bot Responsiveness
# =============================================================================

header "Testing Bot Responsiveness"

info "Testing if bot responds to commands..."

# Check recent bot activity
RECENT_ACTIVITY=$(pm2 logs "$BOT_NAME" --lines 50 2>/dev/null | grep "$(date +%Y-%m-%d)" | wc -l)
echo "Recent activity lines today: $RECENT_ACTIVITY"

if [[ $RECENT_ACTIVITY -gt 0 ]]; then
    log "Bot has recent activity"
    echo "Recent log entries:"
    pm2 logs "$BOT_NAME" --lines 20 2>/dev/null | tail -10
else
    warn "No recent bot activity detected"
fi

echo ""

# =============================================================================
# PHASE 6: Manual Test Instructions
# =============================================================================

header "Manual Testing Instructions"

echo ""
info "🧪 Manual Testing Steps:"
echo ""
echo "1️⃣  Test Basic Bot Response:"
echo "   • Open Telegram and find @Myfnmbot"
echo "   • Send: /start"
echo "   • Expected: Welcome message"
echo "   • If no response: Bot is not receiving messages"
echo ""
echo "2️⃣  Test Help Command:"
echo "   • Send: /help"
echo "   • Expected: Help message with commands"
echo "   • If no response: Command handling is broken"
echo ""
echo "3️⃣  Test Link Command (no code):"
echo "   • Send: /link"
echo "   • Expected: Instructions on how to get auth code"
echo "   • If no response: Link command handler is broken"
echo ""
echo "4️⃣  Test Link Command (invalid code):"
echo "   • Send: /link INVALID123"
echo "   • Expected: Error message about invalid code"
echo "   • If accepts invalid code: Validation is broken"
echo ""
echo "5️⃣  Test Status Command:"
echo "   • Send: /status"
echo "   • Expected: Bot status or 'not linked' message"
echo ""

# =============================================================================
# PHASE 7: Common Issues and Solutions
# =============================================================================

header "Common Issues and Solutions"

echo ""
echo "🔧 Common Issues:"
echo ""
echo "❌ Issue 1: Bot doesn't respond to any commands"
echo "   Solution: Check if bot is polling correctly"
echo "   Command: pm2 restart $BOT_NAME"
echo ""
echo "❌ Issue 2: Bot accepts invalid auth codes"
echo "   Solution: Database validation is not working"
echo "   Check: Supabase connection and auth code table"
echo ""
echo "❌ Issue 3: Valid codes don't work"
echo "   Solution: Auth code generation/verification mismatch"
echo "   Check: Web app and bot are using same database"
echo ""
echo "❌ Issue 4: Status command shows 'not linked' after linking"
echo "   Solution: Account linking didn't save to database"
echo "   Check: telegram_users table in Supabase"
echo ""

# =============================================================================
# PHASE 8: Immediate Actions
# =============================================================================

header "Immediate Actions to Try"

echo ""
echo "🚀 Try these commands now:"
echo ""
echo "1️⃣  Restart the bot:"
echo "   ./manage-bot.sh restart"
echo ""
echo "2️⃣  Check real-time logs:"
echo "   pm2 logs $BOT_NAME --lines 0 --raw"
echo "   (Then test /link command in Telegram)"
echo ""
echo "3️⃣  Test health endpoint:"
echo "   curl http://localhost:$HEALTH_PORT/health | jq ."
echo ""
echo "4️⃣  Check bot status:"
echo "   ./manage-bot.sh status"
echo ""

# =============================================================================
# PHASE 9: Generate Test Report
# =============================================================================

header "Debug Report Summary"

echo ""
echo "📋 Debug Report:"
echo "   Bot Status: $(pm2 list | grep "$BOT_NAME" | awk '{print $9}' || echo 'Unknown')"
echo "   Link Attempts: $LINK_ATTEMPTS"
echo "   Link Errors: $LINK_ERRORS"
echo "   Recent Activity: $RECENT_ACTIVITY lines"
echo "   Health Check: $(curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null && echo 'PASS' || echo 'FAIL')"
echo ""

# Check if main issues are present
ISSUES=0

if ! pm2 list | grep -q "$BOT_NAME.*online"; then
    echo "🚨 CRITICAL: Bot is not online"
    ((ISSUES++))
fi

if ! curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
    echo "🚨 CRITICAL: Health endpoint not responding"
    ((ISSUES++))
fi

if [[ $RECENT_ACTIVITY -eq 0 ]]; then
    echo "⚠️  WARNING: No recent bot activity"
    ((ISSUES++))
fi

if [[ $ISSUES -eq 0 ]]; then
    log "No critical issues detected - problem may be in command handling"
    echo ""
    echo "💡 Next steps:"
    echo "   1. Test bot manually in Telegram"
    echo "   2. Watch logs while testing: pm2 logs $BOT_NAME --lines 0"
    echo "   3. Check if commands appear in logs"
else
    error "$ISSUES critical issues detected"
    echo ""
    echo "🔧 Fix these issues first:"
    echo "   1. Restart bot: ./manage-bot.sh restart"
    echo "   2. Check logs: ./manage-bot.sh logs"
    echo "   3. Verify environment: cat .env"
fi

echo ""
echo "=============================================="
