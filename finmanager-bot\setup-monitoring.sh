#!/bin/bash

# =============================================================================
# FiNManageR Bot - Comprehensive Monitoring Setup
# =============================================================================
# Sets up monitoring, alerting, and health checks for Oracle Cloud E2 Micro
#
# Usage: ./setup-monitoring.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"
MONITOR_DIR="$HOME/monitoring"
LOG_DIR="$HOME/monitoring/logs"

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "🔧 =============================================="
echo "🔧  Setting Up Comprehensive Monitoring"
echo "🔧 =============================================="
echo ""

# =============================================================================
# PHASE 1: Create Monitoring Directory Structure
# =============================================================================

header "Creating Monitoring Infrastructure"

# Create directories
mkdir -p "$MONITOR_DIR"
mkdir -p "$LOG_DIR"
mkdir -p "$MONITOR_DIR/scripts"
mkdir -p "$MONITOR_DIR/alerts"
mkdir -p "$MONITOR_DIR/reports"

log "Monitoring directories created"

# =============================================================================
# PHASE 2: System Health Monitor Script
# =============================================================================

header "Creating System Health Monitor"

cat > "$MONITOR_DIR/scripts/health-monitor.sh" << 'EOF'
#!/bin/bash

# System Health Monitor for E2 Micro
LOG_FILE="$HOME/monitoring/logs/health-$(date +%Y%m%d).log"
ALERT_FILE="$HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log"

# Thresholds
MEMORY_THRESHOLD=85
DISK_THRESHOLD=85
CPU_THRESHOLD=80
RESTART_THRESHOLD=5

# Get current metrics
MEMORY_USAGE=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
DISK_USAGE=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

# Bot status
BOT_STATUS="UNKNOWN"
BOT_MEMORY="0"
BOT_RESTARTS="0"

if pm2 list | grep -q "finmanager-bot.*online"; then
    BOT_STATUS="ONLINE"
    BOT_MEMORY=$(pm2 list | grep "finmanager-bot" | awk '{print $10}' | sed 's/mb//')
    BOT_RESTARTS=$(pm2 list | grep "finmanager-bot" | awk '{print $8}')
else
    BOT_STATUS="OFFLINE"
fi

# Health check
HEALTH_STATUS="FAIL"
if curl -s http://localhost:3001/health &> /dev/null; then
    HEALTH_STATUS="PASS"
fi

# Log metrics
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
echo "$TIMESTAMP,MEMORY,$MEMORY_USAGE,DISK,$DISK_USAGE,CPU,$CPU_USAGE,LOAD,$LOAD_AVG,BOT,$BOT_STATUS,BOT_MEM,$BOT_MEMORY,RESTARTS,$BOT_RESTARTS,HEALTH,$HEALTH_STATUS" >> "$LOG_FILE"

# Check alerts
ALERTS=""

if [[ $MEMORY_USAGE -gt $MEMORY_THRESHOLD ]]; then
    ALERTS="$ALERTS HIGH_MEMORY:${MEMORY_USAGE}%;"
fi

if [[ $DISK_USAGE -gt $DISK_THRESHOLD ]]; then
    ALERTS="$ALERTS HIGH_DISK:${DISK_USAGE}%;"
fi

if [[ $BOT_STATUS == "OFFLINE" ]]; then
    ALERTS="$ALERTS BOT_OFFLINE;"
fi

if [[ $BOT_RESTARTS -gt $RESTART_THRESHOLD ]]; then
    ALERTS="$ALERTS HIGH_RESTARTS:${BOT_RESTARTS};"
fi

if [[ $HEALTH_STATUS == "FAIL" ]]; then
    ALERTS="$ALERTS HEALTH_CHECK_FAIL;"
fi

# Log alerts if any
if [[ -n "$ALERTS" ]]; then
    echo "$TIMESTAMP ALERT: $ALERTS" >> "$ALERT_FILE"
    echo "🚨 ALERT: $ALERTS"
fi

# Output current status
echo "📊 System Status: MEM:${MEMORY_USAGE}% DISK:${DISK_USAGE}% BOT:$BOT_STATUS HEALTH:$HEALTH_STATUS"
EOF

chmod +x "$MONITOR_DIR/scripts/health-monitor.sh"
log "Health monitor script created"

# =============================================================================
# PHASE 3: Bot Performance Monitor
# =============================================================================

header "Creating Bot Performance Monitor"

cat > "$MONITOR_DIR/scripts/bot-monitor.sh" << 'EOF'
#!/bin/bash

# Bot Performance Monitor
LOG_FILE="$HOME/monitoring/logs/bot-performance-$(date +%Y%m%d).log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Get bot metrics
if pm2 list | grep -q "finmanager-bot"; then
    # PM2 metrics
    BOT_STATUS=$(pm2 list | grep "finmanager-bot" | awk '{print $9}')
    BOT_CPU=$(pm2 list | grep "finmanager-bot" | awk '{print $10}')
    BOT_MEMORY=$(pm2 list | grep "finmanager-bot" | awk '{print $11}')
    BOT_UPTIME=$(pm2 list | grep "finmanager-bot" | awk '{print $7}')
    BOT_RESTARTS=$(pm2 list | grep "finmanager-bot" | awk '{print $8}')
    
    # Health check response time
    HEALTH_TIME=$(curl -w "%{time_total}" -s -o /dev/null http://localhost:3001/health)
    
    # Log metrics
    echo "$TIMESTAMP,$BOT_STATUS,$BOT_CPU,$BOT_MEMORY,$BOT_UPTIME,$BOT_RESTARTS,$HEALTH_TIME" >> "$LOG_FILE"
    
    echo "🤖 Bot Performance: Status:$BOT_STATUS CPU:$BOT_CPU Memory:$BOT_MEMORY Uptime:$BOT_UPTIME Restarts:$BOT_RESTARTS Response:${HEALTH_TIME}s"
else
    echo "$TIMESTAMP,OFFLINE,0,0,0,0,0" >> "$LOG_FILE"
    echo "❌ Bot is not running"
fi
EOF

chmod +x "$MONITOR_DIR/scripts/bot-monitor.sh"
log "Bot performance monitor created"

# =============================================================================
# PHASE 4: Account Linking Monitor
# =============================================================================

header "Creating Account Linking Monitor"

cat > "$MONITOR_DIR/scripts/account-linking-monitor.sh" << 'EOF'
#!/bin/bash

# Account Linking Monitor
LOG_FILE="$HOME/monitoring/logs/account-linking-$(date +%Y%m%d).log"
TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')

# Test account linking process
echo "🔗 Testing Account Linking Process..."

# Test 1: Health endpoint
HEALTH_STATUS="FAIL"
if curl -s http://localhost:3001/health | grep -q "healthy"; then
    HEALTH_STATUS="PASS"
fi

# Test 2: Bot responsiveness
BOT_RESPONSIVE="FAIL"
if pm2 list | grep -q "finmanager-bot.*online"; then
    BOT_RESPONSIVE="PASS"
fi

# Test 3: Database connectivity (via health endpoint)
DB_STATUS="UNKNOWN"
HEALTH_RESPONSE=$(curl -s http://localhost:3001/health 2>/dev/null)
if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
    DB_STATUS="CONNECTED"
elif echo "$HEALTH_RESPONSE" | grep -q '"database"'; then
    DB_STATUS="DISCONNECTED"
fi

# Test 4: Check recent bot logs for linking attempts
RECENT_LINKS=$(pm2 logs finmanager-bot --lines 100 2>/dev/null | grep -c "/link" || echo "0")

# Log results
echo "$TIMESTAMP,$HEALTH_STATUS,$BOT_RESPONSIVE,$DB_STATUS,$RECENT_LINKS" >> "$LOG_FILE"

echo "🔗 Account Linking Status:"
echo "   Health: $HEALTH_STATUS"
echo "   Bot: $BOT_RESPONSIVE" 
echo "   Database: $DB_STATUS"
echo "   Recent Links: $RECENT_LINKS"

# Check for issues
if [[ "$HEALTH_STATUS" == "FAIL" || "$BOT_RESPONSIVE" == "FAIL" || "$DB_STATUS" == "DISCONNECTED" ]]; then
    echo "⚠️  Account linking may be impaired"
    echo "$TIMESTAMP ACCOUNT_LINKING_ISSUE: Health:$HEALTH_STATUS Bot:$BOT_RESPONSIVE DB:$DB_STATUS" >> "$HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log"
fi
EOF

chmod +x "$MONITOR_DIR/scripts/account-linking-monitor.sh"
log "Account linking monitor created"

# =============================================================================
# PHASE 5: Automated Monitoring with Cron
# =============================================================================

header "Setting Up Automated Monitoring"

# Create cron jobs
CRON_FILE="/tmp/finmanager-cron"

cat > "$CRON_FILE" << EOF
# FiNManageR Bot Monitoring
# Health check every 5 minutes
*/5 * * * * $MONITOR_DIR/scripts/health-monitor.sh >> $LOG_DIR/cron.log 2>&1

# Bot performance every 10 minutes
*/10 * * * * $MONITOR_DIR/scripts/bot-monitor.sh >> $LOG_DIR/cron.log 2>&1

# Account linking check every 30 minutes
*/30 * * * * $MONITOR_DIR/scripts/account-linking-monitor.sh >> $LOG_DIR/cron.log 2>&1

# Daily cleanup (keep 7 days of logs)
0 2 * * * find $LOG_DIR -name "*.log" -mtime +7 -delete
EOF

# Install cron jobs
crontab "$CRON_FILE"
rm "$CRON_FILE"

log "Automated monitoring scheduled"

# =============================================================================
# PHASE 6: Monitoring Dashboard Script
# =============================================================================

header "Creating Monitoring Dashboard"

cat > "$MONITOR_DIR/dashboard.sh" << 'EOF'
#!/bin/bash

# Monitoring Dashboard
clear

echo "🖥️  =============================================="
echo "🖥️   FiNManageR Bot Monitoring Dashboard"
echo "🖥️  =============================================="
echo ""

# Current system status
echo "📊 Current System Status:"
$HOME/monitoring/scripts/health-monitor.sh
echo ""

# Bot performance
echo "🤖 Bot Performance:"
$HOME/monitoring/scripts/bot-monitor.sh
echo ""

# Account linking status
echo "🔗 Account Linking Status:"
$HOME/monitoring/scripts/account-linking-monitor.sh
echo ""

# Recent alerts
echo "🚨 Recent Alerts (Last 24 hours):"
if [[ -f "$HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log" ]]; then
    tail -10 "$HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log" 2>/dev/null || echo "No alerts today"
else
    echo "No alerts today"
fi
echo ""

# PM2 status
echo "⚙️  PM2 Status:"
pm2 status
echo ""

# System resources
echo "💻 System Resources:"
echo "Memory: $(free -h | awk '/^Mem:/ {print $3 "/" $2}')"
echo "Disk: $(df -h / | awk 'NR==2 {print $3 "/" $2 " (" $5 " used)"}')"
echo "Load: $(uptime | awk -F'load average:' '{print $2}')"
echo ""

echo "🔄 Auto-refresh in 30 seconds... (Ctrl+C to exit)"
EOF

chmod +x "$MONITOR_DIR/dashboard.sh"
log "Monitoring dashboard created"

# =============================================================================
# PHASE 7: Alert Management
# =============================================================================

header "Setting Up Alert Management"

cat > "$MONITOR_DIR/scripts/check-alerts.sh" << 'EOF'
#!/bin/bash

# Alert Management Script
ALERT_FILE="$HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log"

if [[ -f "$ALERT_FILE" ]]; then
    ALERT_COUNT=$(wc -l < "$ALERT_FILE")
    if [[ $ALERT_COUNT -gt 0 ]]; then
        echo "🚨 $ALERT_COUNT alerts today:"
        tail -5 "$ALERT_FILE"
        echo ""
        echo "View all alerts: cat $ALERT_FILE"
    else
        echo "✅ No alerts today"
    fi
else
    echo "✅ No alerts today"
fi
EOF

chmod +x "$MONITOR_DIR/scripts/check-alerts.sh"
log "Alert management setup"

# =============================================================================
# PHASE 8: Initial Test Run
# =============================================================================

header "Running Initial Tests"

# Test all monitoring scripts
echo "Testing health monitor..."
"$MONITOR_DIR/scripts/health-monitor.sh"
echo ""

echo "Testing bot monitor..."
"$MONITOR_DIR/scripts/bot-monitor.sh"
echo ""

echo "Testing account linking monitor..."
"$MONITOR_DIR/scripts/account-linking-monitor.sh"
echo ""

# =============================================================================
# COMPLETION
# =============================================================================

log "✅ Monitoring setup completed successfully!"

echo ""
echo "🎯 =============================================="
echo "🎯  Monitoring Setup Complete!"
echo "🎯 =============================================="
echo ""
echo "📋 Available Commands:"
echo ""
echo "🖥️  Dashboard (real-time monitoring):"
echo "   $MONITOR_DIR/dashboard.sh"
echo ""
echo "🔍 Manual Checks:"
echo "   $MONITOR_DIR/scripts/health-monitor.sh"
echo "   $MONITOR_DIR/scripts/bot-monitor.sh"
echo "   $MONITOR_DIR/scripts/account-linking-monitor.sh"
echo ""
echo "🚨 Check Alerts:"
echo "   $MONITOR_DIR/scripts/check-alerts.sh"
echo ""
echo "📊 Log Files:"
echo "   Health: $LOG_DIR/health-$(date +%Y%m%d).log"
echo "   Bot Performance: $LOG_DIR/bot-performance-$(date +%Y%m%d).log"
echo "   Account Linking: $LOG_DIR/account-linking-$(date +%Y%m%d).log"
echo "   Alerts: $HOME/monitoring/alerts/alerts-$(date +%Y%m%d).log"
echo ""
echo "⏰ Automated Monitoring:"
echo "   ✅ Health checks every 5 minutes"
echo "   ✅ Performance monitoring every 10 minutes"
echo "   ✅ Account linking checks every 30 minutes"
echo "   ✅ Daily log cleanup"
echo ""
echo "🚀 Start monitoring dashboard:"
echo "   $MONITOR_DIR/dashboard.sh"
echo ""
echo "=============================================="
