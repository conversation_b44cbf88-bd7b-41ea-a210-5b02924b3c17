#!/bin/bash

# =============================================================================
# System Monitoring Script for Oracle Cloud E2 Micro
# =============================================================================
# Comprehensive system monitoring for FiNManageR Telegram Bot
# Optimized for E2 Micro (1GB RAM, 1/8 OCPU)
#
# Usage: ./monitor-system.sh [--continuous]
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"
CONTINUOUS_MODE=false

# Check for continuous mode
if [[ "$1" == "--continuous" ]]; then
    CONTINUOUS_MODE=true
fi

# Logging functions
header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Get system information
get_system_info() {
    header "Oracle Cloud E2 Micro System Monitor"
    echo "Timestamp: $(date)"
    echo "Hostname: $(hostname)"
    echo "Uptime: $(uptime -p)"
    echo ""
}

# Memory monitoring
monitor_memory() {
    header "Memory Usage"
    
    # Get memory info
    TOTAL_MEM=$(free -m | awk '/^Mem:/ {print $2}')
    USED_MEM=$(free -m | awk '/^Mem:/ {print $3}')
    FREE_MEM=$(free -m | awk '/^Mem:/ {print $4}')
    AVAILABLE_MEM=$(free -m | awk '/^Mem:/ {print $7}')
    
    # Calculate percentages
    USED_PERCENT=$((USED_MEM * 100 / TOTAL_MEM))
    
    echo "Total Memory: ${TOTAL_MEM}MB"
    echo "Used Memory:  ${USED_MEM}MB (${USED_PERCENT}%)"
    echo "Free Memory:  ${FREE_MEM}MB"
    echo "Available:    ${AVAILABLE_MEM}MB"
    
    # Memory status
    if [[ $USED_PERCENT -lt 70 ]]; then
        log "Memory usage is healthy"
    elif [[ $USED_PERCENT -lt 85 ]]; then
        warn "Memory usage is moderate"
    else
        error "Memory usage is high!"
    fi
    
    echo ""
    free -h
    echo ""
}

# Swap monitoring
monitor_swap() {
    header "Swap Usage"
    
    if [[ $(swapon --show | wc -l) -gt 0 ]]; then
        SWAP_TOTAL=$(free -m | awk '/^Swap:/ {print $2}')
        SWAP_USED=$(free -m | awk '/^Swap:/ {print $3}')
        
        if [[ $SWAP_TOTAL -gt 0 ]]; then
            SWAP_PERCENT=$((SWAP_USED * 100 / SWAP_TOTAL))
            
            echo "Total Swap: ${SWAP_TOTAL}MB"
            echo "Used Swap:  ${SWAP_USED}MB (${SWAP_PERCENT}%)"
            
            if [[ $SWAP_USED -eq 0 ]]; then
                log "Swap available but not in use (optimal)"
            elif [[ $SWAP_PERCENT -lt 50 ]]; then
                info "Swap usage is acceptable"
            else
                warn "High swap usage detected"
            fi
        fi
        
        echo ""
        swapon --show
    else
        error "No swap configured!"
        warn "E2 Micro should have swap for optimal performance"
    fi
    echo ""
}

# CPU monitoring
monitor_cpu() {
    header "CPU Usage"
    
    # Get CPU info
    CPU_CORES=$(nproc)
    LOAD_1MIN=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "CPU Cores: $CPU_CORES (E2 Micro: 1/8 OCPU shared)"
    echo "Load Average (1min): $LOAD_1MIN"
    
    # CPU usage from top
    echo ""
    echo "Current CPU Usage:"
    top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5 $6 $7 $8}'
    
    echo ""
    echo "Top CPU Processes:"
    ps aux --sort=-%cpu | head -6
    echo ""
}

# Disk monitoring
monitor_disk() {
    header "Disk Usage"
    
    # Root filesystem
    DISK_USED=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    DISK_AVAIL=$(df -h / | awk 'NR==2 {print $4}')
    
    echo "Root Filesystem Usage: ${DISK_USED}%"
    echo "Available Space: $DISK_AVAIL"
    
    if [[ $DISK_USED -lt 70 ]]; then
        log "Disk usage is healthy"
    elif [[ $DISK_USED -lt 85 ]]; then
        warn "Disk usage is moderate"
    else
        error "Disk usage is high!"
    fi
    
    echo ""
    df -h /
    
    echo ""
    echo "Largest directories in /home:"
    du -h /home/<USER>/* 2>/dev/null | sort -hr | head -5
    echo ""
}

# Network monitoring
monitor_network() {
    header "Network Status"
    
    # Check listening ports
    echo "Listening Ports:"
    ss -tuln | grep -E ":(22|80|443|3000|3001|8080)"
    
    echo ""
    echo "Bot Health Port Status:"
    if ss -tuln | grep -q ":$HEALTH_PORT"; then
        log "Port $HEALTH_PORT is listening"
    else
        error "Port $HEALTH_PORT is not listening"
    fi
    
    echo ""
    echo "Network Connections:"
    ss -tuln | head -10
    echo ""
}

# Process monitoring
monitor_processes() {
    header "Process Monitoring"
    
    echo "Top Memory Processes:"
    ps aux --sort=-%mem | head -10
    
    echo ""
    echo "Node.js Processes:"
    ps aux | grep node | grep -v grep || echo "No Node.js processes found"
    
    echo ""
    if command -v pm2 &> /dev/null; then
        echo "PM2 Status:"
        pm2 status 2>/dev/null || echo "PM2 not running or no processes"
    else
        warn "PM2 not installed"
    fi
    echo ""
}

# Bot specific monitoring
monitor_bot() {
    header "FiNManageR Bot Status"
    
    # PM2 status
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "$BOT_NAME"; then
            echo "PM2 Bot Status:"
            pm2 list | grep "$BOT_NAME"
            
            echo ""
            echo "Bot Process Details:"
            pm2 show "$BOT_NAME" 2>/dev/null | head -15
        else
            warn "Bot not found in PM2"
        fi
    else
        warn "PM2 not available"
    fi
    
    echo ""
    echo "Health Check:"
    if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
        log "Bot health endpoint responding"
        curl -s "http://localhost:$HEALTH_PORT/health" | jq . 2>/dev/null || curl -s "http://localhost:$HEALTH_PORT/health"
    else
        error "Bot health endpoint not responding"
    fi
    echo ""
}

# System alerts
check_alerts() {
    header "System Alerts"
    
    ALERTS=0
    
    # Memory alert
    USED_PERCENT=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
    if [[ $USED_PERCENT -gt 85 ]]; then
        error "HIGH MEMORY USAGE: ${USED_PERCENT}%"
        ((ALERTS++))
    fi
    
    # Disk alert
    DISK_USED=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [[ $DISK_USED -gt 85 ]]; then
        error "HIGH DISK USAGE: ${DISK_USED}%"
        ((ALERTS++))
    fi
    
    # Swap alert
    if [[ $(swapon --show | wc -l) -eq 0 ]]; then
        warn "NO SWAP CONFIGURED"
        ((ALERTS++))
    fi
    
    # Bot alert
    if ! curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
        error "BOT HEALTH CHECK FAILED"
        ((ALERTS++))
    fi
    
    if [[ $ALERTS -eq 0 ]]; then
        log "No system alerts - all systems healthy!"
    else
        warn "Total alerts: $ALERTS"
    fi
    echo ""
}

# Performance summary
performance_summary() {
    header "Performance Summary"
    
    # Get key metrics
    MEM_USED=$(free | awk '/^Mem:/ {printf "%.0f", $3/$2 * 100}')
    DISK_USED=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    echo "Memory Usage: ${MEM_USED}%"
    echo "Disk Usage:   ${DISK_USED}%"
    echo "Load Average: ${LOAD_AVG}"
    echo "Uptime:       $(uptime -p)"
    
    # Bot status
    if pm2 list 2>/dev/null | grep -q "$BOT_NAME.*online"; then
        echo "Bot Status:   Online ✅"
    else
        echo "Bot Status:   Offline ❌"
    fi
    
    echo ""
}

# Main monitoring function
run_monitor() {
    clear
    get_system_info
    monitor_memory
    monitor_swap
    monitor_cpu
    monitor_disk
    monitor_network
    monitor_processes
    monitor_bot
    check_alerts
    performance_summary
    
    echo "Monitor completed at $(date)"
    echo "=============================================="
}

# Continuous monitoring
continuous_monitor() {
    info "Starting continuous monitoring (Press Ctrl+C to stop)"
    echo "Refresh interval: 30 seconds"
    echo ""
    
    while true; do
        run_monitor
        sleep 30
    done
}

# Help function
show_help() {
    echo "🖥️  Oracle Cloud E2 Micro System Monitor"
    echo ""
    echo "Usage: $0 [--continuous]"
    echo ""
    echo "Options:"
    echo "  (no args)      - Run single monitoring check"
    echo "  --continuous   - Run continuous monitoring (30s intervals)"
    echo "  --help         - Show this help"
    echo ""
    echo "Examples:"
    echo "  ./monitor-system.sh              # Single check"
    echo "  ./monitor-system.sh --continuous # Continuous monitoring"
    echo ""
}

# Main execution
case "$1" in
    --continuous)
        continuous_monitor
        ;;
    --help|-h|help)
        show_help
        ;;
    "")
        run_monitor
        ;;
    *)
        error "Unknown option: $1"
        show_help
        exit 1
        ;;
esac
