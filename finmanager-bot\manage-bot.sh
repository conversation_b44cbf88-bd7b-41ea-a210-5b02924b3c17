#!/bin/bash

# =============================================================================
# FiNManageR Bot Management Script
# =============================================================================
# Complete management script for FiNManageR Telegram Bot
# Optimized for Oracle Cloud E2 Micro deployment
#
# Usage: ./manage-bot.sh [command]
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
BOT_SCRIPT="enterprise-bot.js"
HEALTH_PORT="3001"
LOG_LINES="50"

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Check if PM2 is installed
check_pm2() {
    if ! command -v pm2 &> /dev/null; then
        error "PM2 is not installed. Please run the initialization script first."
        exit 1
    fi
}

# Check if bot directory exists
check_bot_directory() {
    if [[ ! -f "$BOT_SCRIPT" ]]; then
        error "Bot script not found: $BOT_SCRIPT"
        error "Make sure you're running this from the bot directory."
        exit 1
    fi
}

# Start the bot
start_bot() {
    info "🚀 Starting FiNManageR Bot..."
    
    check_pm2
    check_bot_directory
    
    # Check if bot is already running
    if pm2 list | grep -q "$BOT_NAME.*online"; then
        warn "Bot is already running!"
        pm2 status | grep "$BOT_NAME"
        return 0
    fi
    
    # Start with ecosystem config if available, otherwise direct start
    if [[ -f "ecosystem.config.js" ]]; then
        log "Starting with ecosystem configuration..."
        pm2 start ecosystem.config.js
    else
        log "Starting with direct PM2 configuration..."
        pm2 start "$BOT_SCRIPT" \
            --name "$BOT_NAME" \
            --max-memory-restart 800M \
            --node-args="--max-old-space-size=512" \
            --restart-delay=4000 \
            --max-restarts=10 \
            --min-uptime="10s"
    fi
    
    # Wait for startup
    sleep 3
    
    # Verify startup
    if pm2 list | grep -q "$BOT_NAME.*online"; then
        log "Bot started successfully!"
        pm2 status | grep "$BOT_NAME"
    else
        error "Bot failed to start. Check logs with: ./manage-bot.sh logs"
        return 1
    fi
}

# Stop the bot
stop_bot() {
    info "🛑 Stopping FiNManageR Bot..."
    
    check_pm2
    
    if pm2 list | grep -q "$BOT_NAME"; then
        pm2 stop "$BOT_NAME"
        log "Bot stopped successfully!"
    else
        warn "Bot is not running or not found in PM2."
    fi
}

# Restart the bot
restart_bot() {
    info "🔄 Restarting FiNManageR Bot..."
    
    check_pm2
    
    if pm2 list | grep -q "$BOT_NAME"; then
        pm2 restart "$BOT_NAME"
        log "Bot restarted successfully!"
        
        # Wait and verify
        sleep 3
        if pm2 list | grep -q "$BOT_NAME.*online"; then
            log "Bot is running after restart!"
        else
            error "Bot failed to restart properly. Check logs."
        fi
    else
        warn "Bot not found in PM2. Starting fresh..."
        start_bot
    fi
}

# Show bot status
show_status() {
    info "📊 FiNManageR Bot Status:"
    
    check_pm2
    
    echo ""
    echo "=== PM2 Status ==="
    pm2 status
    
    echo ""
    echo "=== Bot Process ==="
    if pm2 list | grep -q "$BOT_NAME"; then
        pm2 list | grep "$BOT_NAME"
        
        # Get detailed info
        echo ""
        echo "=== Detailed Info ==="
        pm2 show "$BOT_NAME" 2>/dev/null | head -20
    else
        warn "Bot not found in PM2 processes."
    fi
    
    echo ""
    echo "=== System Resources ==="
    echo "Memory Usage:"
    free -h | head -2
    echo ""
    echo "CPU Load:"
    uptime
}

# Show bot logs
show_logs() {
    info "📋 FiNManageR Bot Logs (last $LOG_LINES lines):"
    
    check_pm2
    
    if pm2 list | grep -q "$BOT_NAME"; then
        echo ""
        pm2 logs "$BOT_NAME" --lines "$LOG_LINES"
    else
        error "Bot not found in PM2. Cannot show logs."
    fi
}

# Open PM2 monitor
open_monitor() {
    info "📊 Opening PM2 Monitor..."
    
    check_pm2
    
    echo "Press Ctrl+C to exit monitor"
    sleep 2
    pm2 monit
}

# Update bot from repository
update_bot() {
    info "📥 Updating FiNManageR Bot from repository..."
    
    # Check if git repository
    if [[ ! -d ".git" ]]; then
        error "Not a git repository. Cannot update."
        return 1
    fi
    
    # Backup current state
    log "Creating backup..."
    if [[ -f ".env" ]]; then
        cp .env .env.backup.$(date +%s)
        log "Environment file backed up"
    fi
    
    # Pull latest changes
    log "Pulling latest changes..."
    git pull origin main
    
    if [[ $? -eq 0 ]]; then
        log "Repository updated successfully"
        
        # Install/update dependencies
        log "Updating dependencies..."
        NODE_OPTIONS="--max-old-space-size=512" npm install --production
        
        if [[ $? -eq 0 ]]; then
            log "Dependencies updated successfully"
            
            # Restart bot
            log "Restarting bot with new code..."
            restart_bot
            
            log "✅ Bot updated and restarted successfully!"
        else
            error "Failed to update dependencies"
            return 1
        fi
    else
        error "Failed to pull from repository"
        return 1
    fi
}

# Health check
health_check() {
    info "🏥 Checking FiNManageR Bot Health..."
    
    echo ""
    echo "=== Local Health Check ==="
    
    # Check if health port is listening
    if ss -tuln | grep -q ":$HEALTH_PORT"; then
        log "Health port $HEALTH_PORT is listening"
        
        # Try health endpoint
        if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
            log "Health endpoint is responding"
            
            echo ""
            echo "=== Health Response ==="
            curl -s "http://localhost:$HEALTH_PORT/health" | jq . 2>/dev/null || curl -s "http://localhost:$HEALTH_PORT/health"
            
        else
            error "Health endpoint is not responding"
        fi
    else
        error "Health port $HEALTH_PORT is not listening"
    fi
    
    echo ""
    echo "=== PM2 Health ==="
    if pm2 list | grep -q "$BOT_NAME.*online"; then
        log "Bot process is online in PM2"
    else
        error "Bot process is not online in PM2"
    fi
    
    echo ""
    echo "=== External Health Check ==="
    PUBLIC_IP=$(curl -s ifconfig.me 2>/dev/null || echo "Unable to get public IP")
    echo "Public Health URL: http://$PUBLIC_IP:$HEALTH_PORT/health"
    
    if [[ "$PUBLIC_IP" != "Unable to get public IP" ]]; then
        echo "Testing external access..."
        if curl -s --connect-timeout 5 "http://$PUBLIC_IP:$HEALTH_PORT/health" &> /dev/null; then
            log "External health check successful"
        else
            warn "External health check failed (may be firewall/network issue)"
        fi
    fi
}

# System resource check
system_check() {
    info "💻 System Resources Check:"
    
    echo ""
    echo "=== Memory Usage ==="
    free -h
    
    echo ""
    echo "=== Disk Usage ==="
    df -h /
    
    echo ""
    echo "=== CPU Usage ==="
    top -bn1 | grep "Cpu(s)" | awk '{print $2 $3 $4 $5 $6 $7 $8}'
    
    echo ""
    echo "=== System Load ==="
    uptime
    
    echo ""
    echo "=== Swap Usage ==="
    if [[ $(swapon --show | wc -l) -gt 0 ]]; then
        swapon --show
        echo ""
        free -h | grep Swap
    else
        warn "No swap configured"
    fi
    
    echo ""
    echo "=== Top Memory Processes ==="
    ps aux --sort=-%mem | head -10
    
    echo ""
    echo "=== Network Connections ==="
    ss -tuln | grep ":$HEALTH_PORT"
}

# Delete bot from PM2
delete_bot() {
    info "🗑️  Removing FiNManageR Bot from PM2..."
    
    check_pm2
    
    if pm2 list | grep -q "$BOT_NAME"; then
        pm2 stop "$BOT_NAME" 2>/dev/null
        pm2 delete "$BOT_NAME"
        log "Bot removed from PM2 successfully!"
    else
        warn "Bot not found in PM2."
    fi
}

# Save PM2 configuration
save_config() {
    info "💾 Saving PM2 configuration..."
    
    check_pm2
    
    pm2 save
    log "PM2 configuration saved!"
    
    # Setup startup script if not done
    echo ""
    info "Setting up PM2 startup script..."
    pm2 startup | tail -n 1 | bash 2>/dev/null || warn "PM2 startup may need manual configuration"
}

# Show help
show_help() {
    echo "🤖 FiNManageR Bot Management Script"
    echo ""
    echo "Usage: $0 {command}"
    echo ""
    echo "📋 Available Commands:"
    echo ""
    echo "  🚀 start     - Start the bot"
    echo "  🛑 stop      - Stop the bot"
    echo "  🔄 restart   - Restart the bot"
    echo "  📊 status    - Show bot status and system info"
    echo "  📋 logs      - Show recent bot logs"
    echo "  📊 monitor   - Open PM2 real-time monitor"
    echo "  📥 update    - Update bot from git repository"
    echo "  🏥 health    - Check bot health endpoints"
    echo "  💻 system    - Show detailed system resources"
    echo "  🗑️  delete    - Remove bot from PM2"
    echo "  💾 save      - Save PM2 configuration"
    echo "  ❓ help      - Show this help message"
    echo ""
    echo "📖 Examples:"
    echo "  ./manage-bot.sh start"
    echo "  ./manage-bot.sh logs"
    echo "  ./manage-bot.sh health"
    echo ""
    echo "🔗 Quick Status Check:"
    echo "  ./manage-bot.sh status && ./manage-bot.sh health"
    echo ""
}

# Main command handler
case "$1" in
    start)
        start_bot
        ;;
    stop)
        stop_bot
        ;;
    restart)
        restart_bot
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    monitor)
        open_monitor
        ;;
    update)
        update_bot
        ;;
    health)
        health_check
        ;;
    system)
        system_check
        ;;
    delete)
        delete_bot
        ;;
    save)
        save_config
        ;;
    help|--help|-h)
        show_help
        ;;
    *)
        error "Unknown command: $1"
        echo ""
        show_help
        exit 1
        ;;
esac
