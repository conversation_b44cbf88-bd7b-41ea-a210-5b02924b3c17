# 🐧 Ubuntu VM Setup & Troubleshooting Guide

## Current Issue: PM2 not showing finmanager-bot process

This means the bot hasn't been started yet or failed to start. Let's fix this step by step.

## 🔍 Step 1: Check Current Status

```bash
# Check if you're in the right directory
pwd
# Should show: /home/<USER>/finmanager-bot

# Check if PM2 is installed
pm2 --version

# Check current PM2 processes
pm2 list

# Check if bot files exist
ls -la enterprise-bot.js manage-bot.sh .env
```

## 🔧 Step 2: Fix Environment Configuration

Your `.env` file needs to be configured for **production** (not development).

```bash
# Backup current .env if it exists
cp .env .env.backup 2>/dev/null || echo "No existing .env to backup"

# Create production .env file
cat > .env << 'EOF'
# FiNManageR Telegram Bot - Production Environment for Oracle Cloud E2 Micro

# Environment
NODE_ENV=production
PORT=3000
HEALTH_PORT=3001

# Memory optimization for E2 Micro (1GB RAM)
NODE_OPTIONS=--max-old-space-size=512

# Production Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
BOT_USERNAME=Myfnmbot
BOT_NAME=FiNManageR Production Bot

# Supabase Configuration
SUPABASE_URL=https://rprzvyfjdvjoxctawrvo.supabase.co
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.q9zGcZWHB3uhS96o8nB3v9jprEiw3bM3wwoZlRzTDzM

# Production Features
ENABLE_OCR=true
ENABLE_VOICE=true
ENABLE_AI_INSIGHTS=true
ENABLE_PUSH_NOTIFICATIONS=true

# Logging (optimized for production)
LOG_LEVEL=info
LOG_BOT_INTERACTIONS=true
ENABLE_LOGGING=true

# Security
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=30
SESSION_EXPIRE_DAYS=30

# Oracle Cloud E2 Micro specific
ORACLE_CLOUD=true
INSTANCE_TYPE=E2_MICRO

# Monitoring
ENABLE_MONITORING=true
EOF

# Verify the file was created correctly
echo "✅ Production .env file created"
cat .env | head -10
```

## 🚀 Step 3: Start the Bot

```bash
# Method 1: Use the management script (recommended)
./manage-bot.sh start

# Method 2: If manage-bot.sh doesn't exist, start directly
pm2 start enterprise-bot.js --name "finmanager-bot" --max-memory-restart 800M

# Method 3: If ecosystem.config.js exists
pm2 start ecosystem.config.js
```

## 🔍 Step 4: Verify Bot is Running

```bash
# Check PM2 status
pm2 status

# Check specific bot process
pm2 show finmanager-bot

# Check logs
pm2 logs finmanager-bot --lines 20

# Check health endpoint
curl http://localhost:3001/health
```

## 🛠️ Step 5: Troubleshooting Common Issues

### Issue 1: "enterprise-bot.js not found"
```bash
# Check if file exists
ls -la enterprise-bot.js

# If missing, you might be using index.js instead
pm2 start index.js --name "finmanager-bot" --max-memory-restart 800M
```

### Issue 2: "PM2 not installed"
```bash
# Install PM2
sudo npm install -g pm2

# Verify installation
pm2 --version
```

### Issue 3: "Node.js not installed"
```bash
# Check Node.js
node --version

# If not installed, install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs
```

### Issue 4: "Dependencies missing"
```bash
# Install dependencies
npm install --production

# If memory issues during install
NODE_OPTIONS="--max-old-space-size=512" npm install --production
```

### Issue 5: "Port already in use"
```bash
# Check what's using the port
sudo netstat -tulpn | grep :3001

# Kill process if needed
sudo kill -9 $(sudo lsof -t -i:3001)
```

## 📊 Step 6: Monitor and Verify

```bash
# Real-time monitoring
pm2 monit

# System resources
free -h
df -h

# Bot health check
curl http://localhost:3001/health | jq .

# Check if bot responds in Telegram
# Go to Telegram and message @Myfnmbot with /start
```

## 🔄 Step 7: If Still Not Working

### Complete Reset and Restart:
```bash
# Stop all PM2 processes
pm2 stop all
pm2 delete all

# Clear PM2 logs
pm2 flush

# Restart with fresh configuration
pm2 start enterprise-bot.js \
  --name "finmanager-bot" \
  --max-memory-restart 800M \
  --node-args="--max-old-space-size=512" \
  --restart-delay=4000

# Save PM2 configuration
pm2 save

# Setup PM2 to start on boot
pm2 startup
# Follow the command it gives you
```

## 📋 Quick Diagnostic Commands

Run these commands and share the output if you need help:

```bash
echo "=== System Info ==="
uname -a
free -h
df -h /

echo "=== Node.js & PM2 ==="
node --version
npm --version
pm2 --version

echo "=== Bot Files ==="
ls -la ~/finmanager-bot/

echo "=== Environment ==="
head -5 ~/finmanager-bot/.env

echo "=== PM2 Status ==="
pm2 list

echo "=== Network ==="
ss -tuln | grep :3001

echo "=== Logs ==="
pm2 logs finmanager-bot --lines 10 2>/dev/null || echo "No logs found"
```

## 🎯 Expected Results

After following these steps, you should see:

1. **PM2 Status**: `pm2 list` shows `finmanager-bot` as `online`
2. **Health Check**: `curl http://localhost:3001/health` returns JSON response
3. **Telegram**: `@Myfnmbot` responds to `/start` command
4. **Logs**: `pm2 logs finmanager-bot` shows bot activity

## 🆘 If You Need Help

Run the diagnostic commands above and share:
1. The output of `pm2 list`
2. The output of `pm2 logs finmanager-bot --lines 20`
3. The output of `curl http://localhost:3001/health`
4. Any error messages you see

This will help identify the exact issue and provide a targeted solution.
