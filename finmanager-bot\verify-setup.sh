#!/bin/bash

# =============================================================================
# FiNManageR Telegram Bot - Setup Verification Script
# =============================================================================
# This script checks if your Oracle Cloud E2 Micro instance is properly
# configured for running the Telegram bot
#
# Usage: ./verify-setup.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Status tracking
TOTAL_CHECKS=0
PASSED_CHECKS=0
FAILED_CHECKS=0
WARNINGS=0

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_CHECKS++))
    ((TOTAL_CHECKS++))
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((WARNINGS++))
    ((TOTAL_CHECKS++))
}

error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_CHECKS++))
    ((TOTAL_CHECKS++))
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Configuration
BOT_DIR="$HOME/finmanager-bot"
EXPECTED_NODE_VERSION="18"

echo "🔍 =============================================="
echo "🔍  FiNManageR Bot Setup Verification"
echo "🔍 =============================================="
echo ""

# =============================================================================
# SYSTEM INFORMATION
# =============================================================================

info "📊 System Information:"
echo "OS: $(lsb_release -d | cut -f2)"
echo "Kernel: $(uname -r)"
echo "Architecture: $(uname -m)"
echo "CPU Cores: $(nproc)"
echo "Total Memory: $(free -h | awk '/^Mem:/ {print $2}')"
echo "Available Memory: $(free -h | awk '/^Mem:/ {print $7}')"
echo "Disk Space: $(df -h / | awk 'NR==2 {print $4 " available of " $2}')"
echo ""

# =============================================================================
# BASIC SYSTEM CHECKS
# =============================================================================

info "🔧 Checking Basic System Components..."

# Check if running as correct user
if [[ $EUID -eq 0 ]]; then
    error "Running as root user (should be ubuntu user)"
else
    log "Running as non-root user: $(whoami)"
fi

# Check internet connectivity
if ping -c 1 google.com &> /dev/null; then
    log "Internet connectivity working"
else
    error "No internet connection"
fi

# Check essential packages
ESSENTIAL_PACKAGES=("curl" "wget" "git" "htop" "nano" "unzip")
for package in "${ESSENTIAL_PACKAGES[@]}"; do
    if command -v $package &> /dev/null; then
        log "$package is installed"
    else
        error "$package is not installed"
    fi
done

# =============================================================================
# MEMORY AND SWAP CHECKS
# =============================================================================

info "💾 Checking Memory Configuration..."

# Check total memory
TOTAL_MEM=$(free -m | awk '/^Mem:/ {print $2}')
if [[ $TOTAL_MEM -ge 900 && $TOTAL_MEM -le 1100 ]]; then
    log "Memory size appropriate for E2 Micro: ${TOTAL_MEM}MB"
else
    warn "Unexpected memory size: ${TOTAL_MEM}MB (expected ~1024MB)"
fi

# Check swap
if [[ $(swapon --show | wc -l) -gt 0 ]]; then
    SWAP_SIZE=$(free -h | awk '/^Swap:/ {print $2}')
    log "Swap file configured: $SWAP_SIZE"
    
    # Check if swap is being used reasonably
    SWAP_USED=$(free | awk '/^Swap:/ {print $3}')
    if [[ $SWAP_USED -eq 0 ]]; then
        log "Swap available but not currently in use (good)"
    else
        warn "Swap currently in use: $(free -h | awk '/^Swap:/ {print $3}')"
    fi
else
    error "No swap file configured (critical for E2 Micro)"
fi

# =============================================================================
# NODE.JS AND NPM CHECKS
# =============================================================================

info "📦 Checking Node.js Environment..."

# Check Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version | sed 's/v//')
    MAJOR_VERSION=$(echo $NODE_VERSION | cut -d. -f1)
    
    if [[ $MAJOR_VERSION -eq $EXPECTED_NODE_VERSION ]]; then
        log "Node.js version correct: v$NODE_VERSION"
    else
        warn "Node.js version: v$NODE_VERSION (expected v$EXPECTED_NODE_VERSION.x)"
    fi
else
    error "Node.js is not installed"
fi

# Check NPM
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    log "NPM installed: v$NPM_VERSION"
else
    error "NPM is not installed"
fi

# =============================================================================
# PM2 CHECKS
# =============================================================================

info "🔄 Checking PM2 Process Manager..."

if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    log "PM2 installed: v$PM2_VERSION"
    
    # Check PM2 processes
    PM2_PROCESSES=$(pm2 list | grep -c "finmanager-bot" || echo "0")
    if [[ $PM2_PROCESSES -gt 0 ]]; then
        log "FiNManageR bot process found in PM2"
        
        # Check if bot is running
        if pm2 list | grep "finmanager-bot" | grep -q "online"; then
            log "Bot is currently running"
        else
            warn "Bot process exists but not running"
        fi
    else
        warn "No FiNManageR bot process in PM2"
    fi
    
    # Check PM2 startup
    if pm2 startup | grep -q "already"; then
        log "PM2 startup script configured"
    else
        warn "PM2 startup script may not be configured"
    fi
else
    error "PM2 is not installed"
fi

# =============================================================================
# FIREWALL CHECKS
# =============================================================================

info "🔒 Checking Firewall Configuration..."

if command -v ufw &> /dev/null; then
    UFW_STATUS=$(sudo ufw status | head -n 1)
    if echo "$UFW_STATUS" | grep -q "active"; then
        log "UFW firewall is active"
        
        # Check specific rules
        if sudo ufw status | grep -q "3001"; then
            log "Port 3001 allowed in firewall"
        else
            error "Port 3001 not allowed in firewall"
        fi
        
        if sudo ufw status | grep -q "22/tcp"; then
            log "SSH port allowed in firewall"
        else
            warn "SSH port rule not explicitly shown (may be default)"
        fi
    else
        warn "UFW firewall is not active"
    fi
else
    error "UFW firewall is not installed"
fi

# =============================================================================
# BOT REPOSITORY CHECKS
# =============================================================================

info "📁 Checking Bot Repository..."

if [[ -d "$BOT_DIR" ]]; then
    log "Bot directory exists: $BOT_DIR"
    
    cd "$BOT_DIR"
    
    # Check if it's a git repository
    if [[ -d ".git" ]]; then
        log "Git repository initialized"
        
        # Check remote origin
        if git remote -v | grep -q "finmanager-bot"; then
            log "Correct git remote configured"
        else
            warn "Git remote may not be correct"
        fi
    else
        error "Not a git repository"
    fi
    
    # Check main bot files
    BOT_FILES=("enterprise-bot.js" "package.json")
    for file in "${BOT_FILES[@]}"; do
        if [[ -f "$file" ]]; then
            log "$file exists"
        else
            error "$file is missing"
        fi
    done
    
    # Check node_modules
    if [[ -d "node_modules" ]]; then
        log "Dependencies installed (node_modules exists)"
        
        # Check key dependencies
        KEY_DEPS=("node-telegram-bot-api" "@supabase/supabase-js" "express")
        for dep in "${KEY_DEPS[@]}"; do
            if [[ -d "node_modules/$dep" ]]; then
                log "$dep dependency installed"
            else
                error "$dep dependency missing"
            fi
        done
    else
        error "Dependencies not installed (no node_modules)"
    fi
    
else
    error "Bot directory does not exist: $BOT_DIR"
fi

# =============================================================================
# ENVIRONMENT CONFIGURATION CHECKS
# =============================================================================

info "⚙️ Checking Environment Configuration..."

if [[ -f "$BOT_DIR/.env" ]]; then
    log ".env file exists"
    
    # Check critical environment variables
    ENV_VARS=("TELEGRAM_BOT_TOKEN" "SUPABASE_URL" "SUPABASE_SERVICE_ROLE_KEY")
    for var in "${ENV_VARS[@]}"; do
        if grep -q "^$var=" "$BOT_DIR/.env" && ! grep -q "^$var=YOUR_" "$BOT_DIR/.env"; then
            log "$var is configured"
        else
            error "$var is not properly configured"
        fi
    done
    
    # Check if production environment
    if grep -q "NODE_ENV=production" "$BOT_DIR/.env"; then
        log "Production environment configured"
    else
        warn "NODE_ENV may not be set to production"
    fi
    
else
    error ".env file does not exist"
fi

# =============================================================================
# MANAGEMENT SCRIPTS CHECKS
# =============================================================================

info "🛠️ Checking Management Scripts..."

SCRIPTS=("manage-bot.sh" "monitor-system.sh")
for script in "${SCRIPTS[@]}"; do
    if [[ -f "$BOT_DIR/$script" ]]; then
        if [[ -x "$BOT_DIR/$script" ]]; then
            log "$script exists and is executable"
        else
            warn "$script exists but is not executable"
        fi
    else
        error "$script is missing"
    fi
done

# Check PM2 ecosystem file
if [[ -f "$BOT_DIR/ecosystem.config.js" ]]; then
    log "PM2 ecosystem configuration exists"
else
    warn "PM2 ecosystem configuration missing"
fi

# =============================================================================
# NETWORK AND CONNECTIVITY CHECKS
# =============================================================================

info "🌐 Checking Network Configuration..."

# Check if health port is listening
if ss -tuln | grep -q ":3001"; then
    log "Port 3001 is listening (bot health check)"
else
    warn "Port 3001 is not listening (bot may not be running)"
fi

# Try health check if bot is running
if curl -s http://localhost:3001/health &> /dev/null; then
    log "Bot health check endpoint responding"
    
    # Get health check response
    HEALTH_RESPONSE=$(curl -s http://localhost:3001/health)
    if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
        log "Bot reports healthy status"
    else
        warn "Bot health check response unclear"
    fi
else
    warn "Bot health check endpoint not responding"
fi

# =============================================================================
# FINAL SUMMARY
# =============================================================================

echo ""
echo "📊 =============================================="
echo "📊  Verification Summary"
echo "📊 =============================================="
echo ""
echo "Total Checks: $TOTAL_CHECKS"
echo -e "${GREEN}Passed: $PASSED_CHECKS${NC}"
echo -e "${YELLOW}Warnings: $WARNINGS${NC}"
echo -e "${RED}Failed: $FAILED_CHECKS${NC}"
echo ""

# Calculate success percentage
SUCCESS_RATE=$(( (PASSED_CHECKS * 100) / TOTAL_CHECKS ))

if [[ $FAILED_CHECKS -eq 0 ]]; then
    echo -e "${GREEN}🎉 All critical checks passed! Your setup looks good.${NC}"
    if [[ $WARNINGS -gt 0 ]]; then
        echo -e "${YELLOW}⚠️  Some warnings detected - review above for optimization opportunities.${NC}"
    fi
elif [[ $SUCCESS_RATE -ge 80 ]]; then
    echo -e "${YELLOW}⚠️  Setup is mostly complete but has some issues to address.${NC}"
else
    echo -e "${RED}❌ Setup has significant issues that need to be resolved.${NC}"
fi

echo ""
echo "Success Rate: $SUCCESS_RATE%"
echo ""

# =============================================================================
# RECOMMENDATIONS
# =============================================================================

if [[ $FAILED_CHECKS -gt 0 || $WARNINGS -gt 0 ]]; then
    echo "🔧 Recommendations:"
    echo ""
    
    if [[ $FAILED_CHECKS -gt 0 ]]; then
        echo "Critical Issues to Fix:"
        echo "- Run the initialization script if components are missing"
        echo "- Check .env file configuration"
        echo "- Ensure all dependencies are installed"
        echo ""
    fi
    
    if [[ $WARNINGS -gt 0 ]]; then
        echo "Optimizations:"
        echo "- Configure PM2 startup if not done"
        echo "- Start the bot if not running"
        echo "- Review firewall rules"
        echo ""
    fi
    
    echo "Quick fixes:"
    echo "cd $BOT_DIR"
    echo "./manage-bot.sh start    # Start the bot"
    echo "./manage-bot.sh status   # Check status"
    echo "./manage-bot.sh health   # Test health"
    echo ""
fi

echo "=============================================="
