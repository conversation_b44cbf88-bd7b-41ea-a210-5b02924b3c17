#!/bin/bash

# =============================================================================
# FiNManageR Bot - Account Linking Test Suite
# =============================================================================
# Comprehensive testing of the account linking process between web app and bot
#
# Usage: ./test-account-linking.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"
WEBAPP_URL="https://finmanager.netlify.app"

# Test tracking
TOTAL_TESTS=0
PASSED_TESTS=0
FAILED_TESTS=0

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
    ((PASSED_TESTS++))
    ((TOTAL_TESTS++))
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
    ((TOTAL_TESTS++))
}

error() {
    echo -e "${RED}❌ $1${NC}"
    ((FAILED_TESTS++))
    ((TOTAL_TESTS++))
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "🔗 =============================================="
echo "🔗  Account Linking Test Suite"
echo "🔗 =============================================="
echo ""

# =============================================================================
# PHASE 1: Infrastructure Tests
# =============================================================================

header "Testing Infrastructure Components"

# Test 1: Bot Health Check
info "Testing bot health endpoint..."
if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
    HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health")
    if echo "$HEALTH_RESPONSE" | grep -q "healthy"; then
        log "Bot health endpoint responding correctly"
        
        # Check specific features
        if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
            log "Database connection confirmed"
        else
            error "Database connection not confirmed in health check"
        fi
        
        if echo "$HEALTH_RESPONSE" | grep -q '"features"'; then
            log "Bot features endpoint active"
        else
            warn "Bot features not reported in health check"
        fi
    else
        error "Bot health endpoint not returning healthy status"
    fi
else
    error "Bot health endpoint not responding"
fi

# Test 2: PM2 Bot Status
info "Testing PM2 bot status..."
if pm2 list | grep -q "$BOT_NAME.*online"; then
    log "Bot is running in PM2"
    
    # Get bot details
    BOT_MEMORY=$(pm2 list | grep "$BOT_NAME" | awk '{print $11}')
    BOT_UPTIME=$(pm2 list | grep "$BOT_NAME" | awk '{print $7}')
    BOT_RESTARTS=$(pm2 list | grep "$BOT_NAME" | awk '{print $8}')
    
    info "Bot Memory: $BOT_MEMORY"
    info "Bot Uptime: $BOT_UPTIME"
    info "Bot Restarts: $BOT_RESTARTS"
    
    if [[ ${BOT_RESTARTS} -gt 10 ]]; then
        warn "High restart count ($BOT_RESTARTS) - may indicate stability issues"
    else
        log "Bot restart count is acceptable ($BOT_RESTARTS)"
    fi
else
    error "Bot is not running in PM2"
fi

# Test 3: Environment Configuration
info "Testing environment configuration..."
if [[ -f ".env" ]]; then
    log ".env file exists"
    
    # Check critical variables
    if grep -q "TELEGRAM_BOT_TOKEN=**********" .env; then
        log "Production bot token configured"
    else
        error "Production bot token not found in .env"
    fi
    
    if grep -q "BOT_USERNAME=Myfnmbot" .env; then
        log "Production bot username configured"
    else
        error "Production bot username not configured"
    fi
    
    if grep -q "SUPABASE_URL=https://rprzvyfjdvjoxctawrvo.supabase.co" .env; then
        log "Supabase URL configured correctly"
    else
        error "Supabase URL not configured correctly"
    fi
    
    if grep -q "SUPABASE_SERVICE_ROLE_KEY=" .env && ! grep -q "YOUR_" .env; then
        log "Supabase service role key configured"
    else
        error "Supabase service role key not configured"
    fi
    
    if grep -q "NODE_ENV=production" .env; then
        log "Production environment configured"
    else
        warn "Environment not set to production"
    fi
else
    error ".env file not found"
fi

# =============================================================================
# PHASE 2: Bot Command Tests
# =============================================================================

header "Testing Bot Commands (Manual Verification Required)"

echo ""
info "🤖 Manual Bot Testing Instructions:"
echo ""
echo "Please test these commands in Telegram with @Myfnmbot:"
echo ""
echo "1️⃣  Basic Commands:"
echo "   • Send: /start"
echo "   • Expected: Welcome message with feature list"
echo ""
echo "2️⃣  Help Command:"
echo "   • Send: /help"
echo "   • Expected: Comprehensive help with all commands"
echo ""
echo "3️⃣  Status Command:"
echo "   • Send: /status"
echo "   • Expected: Bot status and system information"
echo ""
echo "4️⃣  Link Command (without code):"
echo "   • Send: /link"
echo "   • Expected: Instructions on how to get auth code"
echo ""
echo "5️⃣  Link Command (with invalid code):"
echo "   • Send: /link INVALID123"
echo "   • Expected: Error message about invalid/expired code"
echo ""

# =============================================================================
# PHASE 3: Account Linking Flow Test
# =============================================================================

header "Testing Account Linking Flow"

echo ""
info "🔗 Account Linking Process Test:"
echo ""
echo "To test the complete account linking process:"
echo ""
echo "📱 Step 1: Web App Side"
echo "   1. Open: $WEBAPP_URL"
echo "   2. Login to your FiNManageR account"
echo "   3. Go to: Settings → Telegram Integration"
echo "   4. Click: 'Generate Auth Code'"
echo "   5. Copy the generated 6-character code"
echo ""
echo "🤖 Step 2: Telegram Bot Side"
echo "   1. Open Telegram and find @Myfnmbot"
echo "   2. Send: /link <your-auth-code>"
echo "   3. Expected: Success message with feature list"
echo ""
echo "✅ Step 3: Verification"
echo "   1. Send: /status (should show linked account)"
echo "   2. Try: /balance (should work if linked)"
echo "   3. Try: /expense 100 food Test expense"
echo ""

# =============================================================================
# PHASE 4: Database Integration Tests
# =============================================================================

header "Testing Database Integration"

info "Testing database connectivity through health endpoint..."

HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health" 2>/dev/null)

if [[ -n "$HEALTH_RESPONSE" ]]; then
    echo ""
    info "Health Response Analysis:"
    echo "$HEALTH_RESPONSE" | jq . 2>/dev/null || echo "$HEALTH_RESPONSE"
    echo ""
    
    # Check database status
    if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
        log "Database connection confirmed"
    else
        error "Database connection not confirmed"
    fi
    
    # Check features
    if echo "$HEALTH_RESPONSE" | grep -q '"ocr":"active"'; then
        log "OCR feature active"
    else
        warn "OCR feature not active"
    fi
    
    if echo "$HEALTH_RESPONSE" | grep -q '"voice":"active"'; then
        log "Voice feature active"
    else
        warn "Voice feature not active"
    fi
    
    if echo "$HEALTH_RESPONSE" | grep -q '"ai":"active"'; then
        log "AI features active"
    else
        warn "AI features not active"
    fi
else
    error "No health response received"
fi

# =============================================================================
# PHASE 5: Log Analysis
# =============================================================================

header "Analyzing Bot Logs"

info "Checking recent bot activity..."

if pm2 logs "$BOT_NAME" --lines 50 2>/dev/null | grep -q "error\|Error\|ERROR"; then
    warn "Errors found in recent logs"
    echo ""
    echo "Recent errors:"
    pm2 logs "$BOT_NAME" --lines 50 2>/dev/null | grep -i error | tail -5
    echo ""
else
    log "No recent errors in bot logs"
fi

# Check for successful operations
RECENT_STARTS=$(pm2 logs "$BOT_NAME" --lines 100 2>/dev/null | grep -c "Bot started\|started successfully" || echo "0")
RECENT_HEALTH=$(pm2 logs "$BOT_NAME" --lines 100 2>/dev/null | grep -c "health" || echo "0")
RECENT_LINKS=$(pm2 logs "$BOT_NAME" --lines 100 2>/dev/null | grep -c "/link" || echo "0")

info "Recent activity analysis:"
echo "   Bot starts: $RECENT_STARTS"
echo "   Health checks: $RECENT_HEALTH"
echo "   Link attempts: $RECENT_LINKS"

# =============================================================================
# PHASE 6: Security and Configuration Tests
# =============================================================================

header "Testing Security Configuration"

# Check file permissions
info "Checking file permissions..."
if [[ -r ".env" ]]; then
    ENV_PERMS=$(ls -la .env | awk '{print $1}')
    if [[ "$ENV_PERMS" =~ ^-rw------- ]] || [[ "$ENV_PERMS" =~ ^-rw-rw-r-- ]]; then
        log "Environment file permissions are secure"
    else
        warn "Environment file permissions may be too open: $ENV_PERMS"
    fi
else
    error "Cannot read .env file"
fi

# Check for sensitive data exposure
info "Checking for sensitive data exposure..."
if ps aux | grep -v grep | grep -q "TELEGRAM_BOT_TOKEN\|SUPABASE_SERVICE_ROLE_KEY"; then
    warn "Sensitive data may be visible in process list"
else
    log "No sensitive data visible in process list"
fi

# =============================================================================
# PHASE 7: Performance Tests
# =============================================================================

header "Testing Performance"

info "Testing response times..."

# Health endpoint response time
HEALTH_TIME=$(curl -w "%{time_total}" -s -o /dev/null "http://localhost:$HEALTH_PORT/health" 2>/dev/null)
if [[ -n "$HEALTH_TIME" ]]; then
    if (( $(echo "$HEALTH_TIME < 2.0" | bc -l) )); then
        log "Health endpoint response time: ${HEALTH_TIME}s (good)"
    else
        warn "Health endpoint response time: ${HEALTH_TIME}s (slow)"
    fi
else
    error "Could not measure health endpoint response time"
fi

# Memory usage check
BOT_MEMORY_MB=$(pm2 list | grep "$BOT_NAME" | awk '{print $11}' | sed 's/mb//' 2>/dev/null)
if [[ -n "$BOT_MEMORY_MB" ]]; then
    if [[ $BOT_MEMORY_MB -lt 200 ]]; then
        log "Bot memory usage: ${BOT_MEMORY_MB}MB (excellent)"
    elif [[ $BOT_MEMORY_MB -lt 400 ]]; then
        log "Bot memory usage: ${BOT_MEMORY_MB}MB (good)"
    elif [[ $BOT_MEMORY_MB -lt 600 ]]; then
        warn "Bot memory usage: ${BOT_MEMORY_MB}MB (moderate)"
    else
        error "Bot memory usage: ${BOT_MEMORY_MB}MB (high)"
    fi
else
    warn "Could not determine bot memory usage"
fi

# =============================================================================
# PHASE 8: Test Results Summary
# =============================================================================

echo ""
echo "📊 =============================================="
echo "📊  Test Results Summary"
echo "📊 =============================================="
echo ""

echo "Total Tests: $TOTAL_TESTS"
echo -e "${GREEN}Passed: $PASSED_TESTS${NC}"
echo -e "${RED}Failed: $FAILED_TESTS${NC}"

if [[ $FAILED_TESTS -eq 0 ]]; then
    echo ""
    echo -e "${GREEN}🎉 All automated tests passed!${NC}"
    echo ""
    echo "✅ Your bot infrastructure is ready for account linking"
    echo ""
    echo "🔗 Next Steps:"
    echo "   1. Test manual bot commands in Telegram"
    echo "   2. Test complete account linking flow"
    echo "   3. Verify transaction logging works"
    echo "   4. Set up monitoring: ./setup-monitoring.sh"
else
    echo ""
    echo -e "${RED}❌ Some tests failed - please review and fix issues${NC}"
    echo ""
    echo "🔧 Common fixes:"
    echo "   • Check .env configuration"
    echo "   • Restart bot: ./manage-bot.sh restart"
    echo "   • Check logs: ./manage-bot.sh logs"
fi

echo ""
echo "📋 Manual Testing Checklist:"
echo "   □ /start command works"
echo "   □ /help shows all commands"
echo "   □ /status shows bot info"
echo "   □ /link shows instructions"
echo "   □ Web app generates auth codes"
echo "   □ /link <code> successfully links account"
echo "   □ /balance works after linking"
echo "   □ Transaction commands work"
echo ""

SUCCESS_RATE=$(( (PASSED_TESTS * 100) / TOTAL_TESTS ))
echo "Success Rate: $SUCCESS_RATE%"
echo ""
echo "=============================================="
