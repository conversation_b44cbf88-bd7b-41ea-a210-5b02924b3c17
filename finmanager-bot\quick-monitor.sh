#!/bin/bash

# =============================================================================
# FiNManageR Bot - Quick Monitoring Dashboard
# =============================================================================
# Real-time monitoring dashboard for Oracle Cloud E2 Micro
#
# Usage: ./quick-monitor.sh [--continuous]
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
MAGENTA='\033[0;35m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"
REFRESH_INTERVAL=10

# Check for continuous mode
CONTINUOUS_MODE=false
if [[ "$1" == "--continuous" ]]; then
    CONTINUOUS_MODE=true
fi

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

highlight() {
    echo -e "${MAGENTA}$1${NC}"
}

# Get system metrics
get_system_metrics() {
    # Memory
    TOTAL_MEM=$(free -m | awk '/^Mem:/ {print $2}')
    USED_MEM=$(free -m | awk '/^Mem:/ {print $3}')
    FREE_MEM=$(free -m | awk '/^Mem:/ {print $4}')
    AVAILABLE_MEM=$(free -m | awk '/^Mem:/ {print $7}')
    MEM_PERCENT=$((USED_MEM * 100 / TOTAL_MEM))
    
    # Swap
    SWAP_TOTAL=$(free -m | awk '/^Swap:/ {print $2}')
    SWAP_USED=$(free -m | awk '/^Swap:/ {print $3}')
    SWAP_PERCENT=0
    if [[ $SWAP_TOTAL -gt 0 ]]; then
        SWAP_PERCENT=$((SWAP_USED * 100 / SWAP_TOTAL))
    fi
    
    # Disk
    DISK_USED=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    DISK_AVAIL=$(df -h / | awk 'NR==2 {print $4}')
    
    # CPU Load
    LOAD_1MIN=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    # Uptime
    SYSTEM_UPTIME=$(uptime -p)
}

# Get bot metrics
get_bot_metrics() {
    if pm2 list | grep -q "$BOT_NAME"; then
        BOT_STATUS=$(pm2 list | grep "$BOT_NAME" | awk '{print $9}')
        BOT_CPU=$(pm2 list | grep "$BOT_NAME" | awk '{print $10}')
        BOT_MEMORY=$(pm2 list | grep "$BOT_NAME" | awk '{print $11}')
        BOT_UPTIME=$(pm2 list | grep "$BOT_NAME" | awk '{print $7}')
        BOT_RESTARTS=$(pm2 list | grep "$BOT_NAME" | awk '{print $8}')
        BOT_PID=$(pm2 list | grep "$BOT_NAME" | awk '{print $6}')
    else
        BOT_STATUS="offline"
        BOT_CPU="0%"
        BOT_MEMORY="0mb"
        BOT_UPTIME="0"
        BOT_RESTARTS="0"
        BOT_PID="N/A"
    fi
}

# Get health metrics
get_health_metrics() {
    HEALTH_STATUS="FAIL"
    HEALTH_RESPONSE=""
    DB_STATUS="UNKNOWN"
    FEATURES_STATUS="UNKNOWN"
    RESPONSE_TIME="N/A"
    
    if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
        HEALTH_STATUS="PASS"
        HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health")
        RESPONSE_TIME=$(curl -w "%{time_total}" -s -o /dev/null "http://localhost:$HEALTH_PORT/health")
        
        if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
            DB_STATUS="CONNECTED"
        elif echo "$HEALTH_RESPONSE" | grep -q '"database"'; then
            DB_STATUS="DISCONNECTED"
        fi
        
        if echo "$HEALTH_RESPONSE" | grep -q '"features"'; then
            FEATURES_STATUS="ACTIVE"
        fi
    fi
}

# Display dashboard
display_dashboard() {
    clear
    
    echo -e "${CYAN}🖥️  =============================================="
    echo -e "🖥️   FiNManageR Bot Monitoring Dashboard"
    echo -e "🖥️  =============================================="
    echo -e "📅 $(date)"
    echo -e "🏠 $(hostname) | 🌐 $(curl -s ifconfig.me 2>/dev/null || echo 'IP Unknown')"
    echo -e "=============================================="
    echo -e "${NC}"
    
    # System Status
    header "System Status (Oracle Cloud E2 Micro)"
    
    # Memory status with color coding
    if [[ $MEM_PERCENT -lt 70 ]]; then
        echo -e "💾 Memory: ${GREEN}${USED_MEM}MB/${TOTAL_MEM}MB (${MEM_PERCENT}%)${NC} | Available: ${AVAILABLE_MEM}MB"
    elif [[ $MEM_PERCENT -lt 85 ]]; then
        echo -e "💾 Memory: ${YELLOW}${USED_MEM}MB/${TOTAL_MEM}MB (${MEM_PERCENT}%)${NC} | Available: ${AVAILABLE_MEM}MB"
    else
        echo -e "💾 Memory: ${RED}${USED_MEM}MB/${TOTAL_MEM}MB (${MEM_PERCENT}%)${NC} | Available: ${AVAILABLE_MEM}MB"
    fi
    
    # Swap status
    if [[ $SWAP_TOTAL -gt 0 ]]; then
        if [[ $SWAP_USED -eq 0 ]]; then
            echo -e "🔄 Swap: ${GREEN}${SWAP_USED}MB/${SWAP_TOTAL}MB (${SWAP_PERCENT}%)${NC} - Not in use"
        elif [[ $SWAP_PERCENT -lt 50 ]]; then
            echo -e "🔄 Swap: ${YELLOW}${SWAP_USED}MB/${SWAP_TOTAL}MB (${SWAP_PERCENT}%)${NC}"
        else
            echo -e "🔄 Swap: ${RED}${SWAP_USED}MB/${SWAP_TOTAL}MB (${SWAP_PERCENT}%)${NC}"
        fi
    else
        echo -e "🔄 Swap: ${RED}Not configured${NC}"
    fi
    
    # Disk status
    if [[ $DISK_USED -lt 70 ]]; then
        echo -e "💽 Disk: ${GREEN}${DISK_USED}% used${NC} | Available: ${DISK_AVAIL}"
    elif [[ $DISK_USED -lt 85 ]]; then
        echo -e "💽 Disk: ${YELLOW}${DISK_USED}% used${NC} | Available: ${DISK_AVAIL}"
    else
        echo -e "💽 Disk: ${RED}${DISK_USED}% used${NC} | Available: ${DISK_AVAIL}"
    fi
    
    echo -e "⚡ Load: ${LOAD_1MIN} | ⏰ Uptime: ${SYSTEM_UPTIME}"
    echo ""
    
    # Bot Status
    header "Bot Status (@Myfnmbot)"
    
    if [[ "$BOT_STATUS" == "online" ]]; then
        echo -e "🤖 Status: ${GREEN}${BOT_STATUS}${NC} | PID: ${BOT_PID}"
        echo -e "💻 CPU: ${BOT_CPU} | 🧠 Memory: ${BOT_MEMORY} | ⏱️  Uptime: ${BOT_UPTIME}"
        
        if [[ $BOT_RESTARTS -eq 0 ]]; then
            echo -e "🔄 Restarts: ${GREEN}${BOT_RESTARTS}${NC}"
        elif [[ $BOT_RESTARTS -lt 5 ]]; then
            echo -e "🔄 Restarts: ${YELLOW}${BOT_RESTARTS}${NC}"
        else
            echo -e "🔄 Restarts: ${RED}${BOT_RESTARTS}${NC}"
        fi
    else
        echo -e "🤖 Status: ${RED}${BOT_STATUS}${NC}"
        echo -e "❌ Bot is not running!"
    fi
    echo ""
    
    # Health Status
    header "Health & Connectivity"
    
    if [[ "$HEALTH_STATUS" == "PASS" ]]; then
        echo -e "🏥 Health Check: ${GREEN}PASS${NC} | Response Time: ${RESPONSE_TIME}s"
    else
        echo -e "🏥 Health Check: ${RED}FAIL${NC}"
    fi
    
    if [[ "$DB_STATUS" == "CONNECTED" ]]; then
        echo -e "🗄️  Database: ${GREEN}CONNECTED${NC}"
    elif [[ "$DB_STATUS" == "DISCONNECTED" ]]; then
        echo -e "🗄️  Database: ${RED}DISCONNECTED${NC}"
    else
        echo -e "🗄️  Database: ${YELLOW}UNKNOWN${NC}"
    fi
    
    if [[ "$FEATURES_STATUS" == "ACTIVE" ]]; then
        echo -e "⚙️  Features: ${GREEN}ACTIVE${NC}"
    else
        echo -e "⚙️  Features: ${YELLOW}UNKNOWN${NC}"
    fi
    
    # Port status
    if ss -tuln | grep -q ":$HEALTH_PORT"; then
        echo -e "🌐 Port $HEALTH_PORT: ${GREEN}LISTENING${NC}"
    else
        echo -e "🌐 Port $HEALTH_PORT: ${RED}NOT LISTENING${NC}"
    fi
    echo ""
    
    # Recent Activity
    header "Recent Activity"
    
    # Check recent logs for activity
    if pm2 logs "$BOT_NAME" --lines 10 2>/dev/null | grep -q "$(date +%Y-%m-%d)"; then
        echo -e "📋 Recent Logs: ${GREEN}Active${NC}"
        
        # Count recent activities
        RECENT_ERRORS=$(pm2 logs "$BOT_NAME" --lines 50 2>/dev/null | grep -c "error\|Error\|ERROR" || echo "0")
        RECENT_COMMANDS=$(pm2 logs "$BOT_NAME" --lines 50 2>/dev/null | grep -c "/start\|/help\|/link\|/status" || echo "0")
        
        echo -e "🔍 Recent Commands: ${RECENT_COMMANDS} | ❌ Recent Errors: ${RECENT_ERRORS}"
    else
        echo -e "📋 Recent Logs: ${YELLOW}No recent activity${NC}"
    fi
    echo ""
    
    # Alerts
    header "System Alerts"
    
    ALERTS=()
    
    if [[ $MEM_PERCENT -gt 85 ]]; then
        ALERTS+=("HIGH_MEMORY:${MEM_PERCENT}%")
    fi
    
    if [[ $DISK_USED -gt 85 ]]; then
        ALERTS+=("HIGH_DISK:${DISK_USED}%")
    fi
    
    if [[ "$BOT_STATUS" != "online" ]]; then
        ALERTS+=("BOT_OFFLINE")
    fi
    
    if [[ $BOT_RESTARTS -gt 10 ]]; then
        ALERTS+=("HIGH_RESTARTS:${BOT_RESTARTS}")
    fi
    
    if [[ "$HEALTH_STATUS" != "PASS" ]]; then
        ALERTS+=("HEALTH_CHECK_FAIL")
    fi
    
    if [[ ${#ALERTS[@]} -eq 0 ]]; then
        echo -e "✅ ${GREEN}No alerts - All systems healthy${NC}"
    else
        echo -e "🚨 ${RED}Active Alerts:${NC}"
        for alert in "${ALERTS[@]}"; do
            echo -e "   • ${RED}$alert${NC}"
        done
    fi
    echo ""
    
    # Quick Actions
    header "Quick Actions"
    echo "🔧 Bot Management:"
    echo "   ./manage-bot.sh status    # Detailed bot status"
    echo "   ./manage-bot.sh logs      # View recent logs"
    echo "   ./manage-bot.sh restart   # Restart bot"
    echo "   ./manage-bot.sh health    # Health check"
    echo ""
    echo "🔍 Testing:"
    echo "   ./test-account-linking.sh # Test account linking"
    echo "   ./verify-setup.sh         # Verify setup"
    echo ""
    echo "📊 Monitoring:"
    echo "   ./setup-monitoring.sh     # Setup full monitoring"
    echo "   ./monitor-system.sh       # System monitor"
    echo ""
    
    if [[ "$CONTINUOUS_MODE" == true ]]; then
        echo -e "${YELLOW}🔄 Auto-refresh in ${REFRESH_INTERVAL} seconds... (Ctrl+C to exit)${NC}"
        echo "=============================================="
    else
        echo "💡 Run with --continuous for auto-refresh"
        echo "=============================================="
    fi
}

# Main execution
if [[ "$CONTINUOUS_MODE" == true ]]; then
    info "Starting continuous monitoring mode..."
    while true; do
        get_system_metrics
        get_bot_metrics
        get_health_metrics
        display_dashboard
        sleep $REFRESH_INTERVAL
    done
else
    get_system_metrics
    get_bot_metrics
    get_health_metrics
    display_dashboard
fi
