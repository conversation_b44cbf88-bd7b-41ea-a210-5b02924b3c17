#!/bin/bash

# =============================================================================
# FiNManageR Bot - Account Linking Fix Script
# =============================================================================
# Fix common account linking issues
#
# Usage: ./fix-account-linking.sh
#
# Author: FiNManageR Team
# Version: 1.0.0
# =============================================================================

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Configuration
BOT_NAME="finmanager-bot"
HEALTH_PORT="3001"

# Logging functions
log() {
    echo -e "${GREEN}✅ $1${NC}"
}

warn() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

error() {
    echo -e "${RED}❌ $1${NC}"
}

info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

header() {
    echo -e "${CYAN}=== $1 ===${NC}"
}

echo "🔧 =============================================="
echo "🔧  Account Linking Fix Script"
echo "🔧 =============================================="
echo ""

# =============================================================================
# PHASE 1: Immediate Diagnosis
# =============================================================================

header "Quick Diagnosis"

# Check bot status
if pm2 list | grep -q "$BOT_NAME.*online"; then
    log "Bot is running"
else
    error "Bot is not running - starting it now"
    ./manage-bot.sh start
    sleep 3
fi

# Check health
if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
    log "Health endpoint responding"
else
    error "Health endpoint not responding"
fi

# Check recent activity
RECENT_COMMANDS=$(pm2 logs "$BOT_NAME" --lines 100 2>/dev/null | grep -c "/start\|/help\|/link\|/status" || echo "0")
echo "Recent commands in logs: $RECENT_COMMANDS"

if [[ $RECENT_COMMANDS -eq 0 ]]; then
    warn "No recent commands detected - bot may not be receiving messages"
fi

echo ""

# =============================================================================
# PHASE 2: Check Bot Script Implementation
# =============================================================================

header "Checking Bot Implementation"

# Determine which script is running
SCRIPT_PATH=$(pm2 show "$BOT_NAME" 2>/dev/null | grep "script" | awk '{print $3}' | head -1)
echo "Bot script: $SCRIPT_PATH"

if [[ -f "$SCRIPT_PATH" ]]; then
    # Check if it's the enterprise bot
    if [[ "$SCRIPT_PATH" == *"enterprise-bot.js"* ]]; then
        log "Using enterprise-bot.js"
        
        # Check if enterprise bot has proper link implementation
        if grep -q "handleLinkAccount" "$SCRIPT_PATH"; then
            log "Enterprise bot has link handler"
        else
            warn "Enterprise bot missing link handler"
        fi
        
    elif [[ "$SCRIPT_PATH" == *"index.js"* ]]; then
        log "Using index.js"
        
        # Check if index.js has proper link implementation
        if grep -q "/link" "$SCRIPT_PATH"; then
            log "Index.js has link command"
        else
            warn "Index.js missing link command"
        fi
    else
        warn "Unknown bot script: $SCRIPT_PATH"
    fi
else
    error "Bot script not found: $SCRIPT_PATH"
fi

echo ""

# =============================================================================
# PHASE 3: Test Real-Time Logging
# =============================================================================

header "Testing Real-Time Command Detection"

echo ""
info "🧪 Real-Time Test Instructions:"
echo ""
echo "I'll start monitoring logs in real-time."
echo "Please do the following in Telegram (@Myfnmbot):"
echo ""
echo "1. Send: /start"
echo "2. Send: /help" 
echo "3. Send: /link"
echo "4. Send: /link INVALID123"
echo "5. Send: /status"
echo ""
echo "Press ENTER when ready to start monitoring..."
read -r

echo "🔍 Starting real-time log monitoring..."
echo "Send commands to @Myfnmbot now. Press Ctrl+C to stop monitoring."
echo ""

# Start real-time log monitoring
timeout 60 pm2 logs "$BOT_NAME" --lines 0 --raw 2>/dev/null || echo "Monitoring stopped"

echo ""
echo "Monitoring stopped."
echo ""

# =============================================================================
# PHASE 4: Check for Common Issues
# =============================================================================

header "Checking for Common Issues"

# Issue 1: Wrong bot token
info "Checking bot token..."
if grep -q "TELEGRAM_BOT_TOKEN=**********" .env; then
    log "Production bot token configured"
else
    error "Wrong bot token - should be production token"
    echo "Current token starts with:"
    grep "TELEGRAM_BOT_TOKEN" .env | cut -d'=' -f2 | cut -c1-15
    echo ""
    echo "Should start with: **********"
fi

# Issue 2: Database connection
info "Checking database connection..."
HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health" 2>/dev/null)
if echo "$HEALTH_RESPONSE" | grep -q '"database":"connected"'; then
    log "Database connected"
else
    error "Database not connected"
    echo "Health response: $HEALTH_RESPONSE"
fi

# Issue 3: Environment mismatch
info "Checking environment..."
if grep -q "NODE_ENV=production" .env; then
    log "Production environment set"
else
    warn "Environment not set to production"
    grep "NODE_ENV" .env || echo "NODE_ENV not set"
fi

echo ""

# =============================================================================
# PHASE 5: Apply Fixes
# =============================================================================

header "Applying Fixes"

# Fix 1: Restart bot with fresh environment
info "Restarting bot with fresh environment..."
./manage-bot.sh restart
sleep 5

if pm2 list | grep -q "$BOT_NAME.*online"; then
    log "Bot restarted successfully"
else
    error "Bot failed to restart"
fi

# Fix 2: Clear PM2 logs to start fresh
info "Clearing old logs..."
pm2 flush "$BOT_NAME" 2>/dev/null || echo "Could not clear logs"

# Fix 3: Test health endpoint again
info "Testing health endpoint after restart..."
sleep 3
if curl -s "http://localhost:$HEALTH_PORT/health" &> /dev/null; then
    log "Health endpoint responding after restart"
    HEALTH_RESPONSE=$(curl -s "http://localhost:$HEALTH_PORT/health")
    echo "Health status: $(echo "$HEALTH_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)"
else
    error "Health endpoint still not responding"
fi

echo ""

# =============================================================================
# PHASE 6: Final Test Instructions
# =============================================================================

header "Final Test Instructions"

echo ""
info "🎯 Now test the bot again:"
echo ""
echo "1️⃣  Basic Test:"
echo "   • Send: /start to @Myfnmbot"
echo "   • Should get welcome message"
echo ""
echo "2️⃣  Link Test (no code):"
echo "   • Send: /link"
echo "   • Should get instructions"
echo ""
echo "3️⃣  Link Test (invalid code):"
echo "   • Send: /link INVALID123"
echo "   • Should get error message"
echo ""
echo "4️⃣  Status Test:"
echo "   • Send: /status"
echo "   • Should get bot status"
echo ""
echo "5️⃣  Monitor logs while testing:"
echo "   pm2 logs $BOT_NAME --lines 0"
echo ""

# =============================================================================
# PHASE 7: Alternative Solutions
# =============================================================================

header "Alternative Solutions"

echo ""
echo "🔄 If issues persist, try these:"
echo ""
echo "1️⃣  Switch to index.js (simpler implementation):"
echo "   pm2 stop $BOT_NAME"
echo "   pm2 start index.js --name $BOT_NAME --max-memory-restart 800M"
echo ""
echo "2️⃣  Check Supabase database tables:"
echo "   • telegram_auth_codes table exists"
echo "   • telegram_users table exists"
echo "   • Database functions are created"
echo ""
echo "3️⃣  Test with development bot first:"
echo "   • Change TELEGRAM_BOT_TOKEN to development token"
echo "   • Test with @Fnmsandbox_bot"
echo "   • Switch back to production when working"
echo ""
echo "4️⃣  Manual database test:"
echo "   • Generate auth code in web app"
echo "   • Check if code appears in telegram_auth_codes table"
echo "   • Test bot with that specific code"
echo ""

# =============================================================================
# PHASE 8: Summary
# =============================================================================

header "Summary"

echo ""
echo "📋 What we did:"
echo "   ✅ Checked bot status and restarted"
echo "   ✅ Verified environment configuration"
echo "   ✅ Cleared old logs"
echo "   ✅ Tested health endpoint"
echo ""
echo "🔍 Next steps:"
echo "   1. Test bot commands manually"
echo "   2. Watch logs: pm2 logs $BOT_NAME --lines 0"
echo "   3. If still not working, run: ./debug-account-linking.sh"
echo ""
echo "🆘 If you need help:"
echo "   Share the output of:"
echo "   • pm2 logs $BOT_NAME --lines 20"
echo "   • curl http://localhost:$HEALTH_PORT/health"
echo "   • What happens when you send /start to @Myfnmbot"
echo ""
echo "=============================================="
